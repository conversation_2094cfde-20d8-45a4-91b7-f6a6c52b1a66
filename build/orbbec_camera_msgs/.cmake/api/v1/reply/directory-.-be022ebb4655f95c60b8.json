{"backtraceGraph": {"commands": ["install", "ament_index_register_resource", "rosidl_generate_interfaces", "include", "ament_execute_extensions", "ament_environment_hooks", "_ament_cmake_export_libraries_register_environment_hook", "ament_export_libraries", "_ament_cmake_python_register_environment_hook", "ament_python_install_package", "_ament_cmake_python_install_package", "ament_cmake_environment_generate_package_run_dependencies_marker", "ament_package", "ament_cmake_environment_generate_parent_prefix_path_marker", "ament_generate_package_environment", "ament_index_register_package", "_ament_package"], "files": ["/opt/ros/jazzy/share/ament_cmake_core/cmake/index/ament_index_register_resource.cmake", "/opt/ros/jazzy/share/rosidl_cmake/cmake/rosidl_generate_interfaces.cmake", "CMakeLists.txt", "/opt/ros/jazzy/share/rosidl_generator_type_description/cmake/rosidl_generator_type_description_generate_interfaces.cmake", "/opt/ros/jazzy/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake", "/opt/ros/jazzy/share/rosidl_generator_c/cmake/rosidl_generator_c_generate_interfaces.cmake", "/opt/ros/jazzy/share/ament_cmake_core/cmake/environment_hooks/ament_environment_hooks.cmake", "/opt/ros/jazzy/share/ament_cmake_export_libraries/cmake/ament_cmake_export_libraries-extras.cmake", "/opt/ros/jazzy/share/ament_cmake_export_libraries/cmake/ament_export_libraries.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_fastrtps_c/cmake/rosidl_typesupport_fastrtps_c_generate_interfaces.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_introspection_c/cmake/rosidl_typesupport_introspection_c_generate_interfaces.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_c/cmake/rosidl_typesupport_c_generate_interfaces.cmake", "/opt/ros/jazzy/share/rosidl_generator_cpp/cmake/rosidl_generator_cpp_generate_interfaces.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_fastrtps_cpp/cmake/rosidl_typesupport_fastrtps_cpp_generate_interfaces.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_introspection_cpp/cmake/rosidl_typesupport_introspection_cpp_generate_interfaces.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_cpp/cmake/rosidl_typesupport_cpp_generate_interfaces.cmake", "/opt/ros/jazzy/share/ament_cmake_python/cmake/ament_cmake_python-extras.cmake", "/opt/ros/jazzy/share/ament_cmake_python/cmake/ament_python_install_package.cmake", "/opt/ros/jazzy/share/rosidl_generator_py/cmake/rosidl_generator_py_generate_interfaces.cmake", "/opt/ros/jazzy/share/ament_cmake_core/cmake/environment/ament_cmake_environment_package_hook.cmake", "/opt/ros/jazzy/share/ament_cmake_core/cmake/core/ament_package.cmake", "/opt/ros/jazzy/share/ament_cmake_core/cmake/environment_hooks/ament_cmake_environment_hooks_package_hook.cmake", "/opt/ros/jazzy/share/ament_cmake_core/cmake/environment_hooks/ament_generate_package_environment.cmake", "/opt/ros/jazzy/share/ament_cmake_core/cmake/index/ament_index_register_package.cmake", "/opt/ros/jazzy/share/ament_cmake_core/cmake/index/ament_cmake_index_package_hook.cmake", "/opt/ros/jazzy/share/ament_cmake_export_targets/cmake/ament_cmake_export_targets_package_hook.cmake"], "nodes": [{"file": 2}, {"command": 2, "file": 2, "line": 17, "parent": 0}, {"command": 1, "file": 1, "line": 246, "parent": 1}, {"command": 0, "file": 0, "line": 105, "parent": 2}, {"command": 4, "file": 1, "line": 280, "parent": 1}, {"command": 3, "file": 4, "line": 48, "parent": 4}, {"file": 3, "parent": 5}, {"command": 0, "file": 3, "line": 103, "parent": 6}, {"command": 0, "file": 3, "line": 103, "parent": 6}, {"command": 0, "file": 3, "line": 103, "parent": 6}, {"command": 0, "file": 3, "line": 103, "parent": 6}, {"command": 0, "file": 3, "line": 103, "parent": 6}, {"command": 0, "file": 3, "line": 103, "parent": 6}, {"command": 0, "file": 3, "line": 103, "parent": 6}, {"command": 0, "file": 3, "line": 103, "parent": 6}, {"command": 0, "file": 3, "line": 103, "parent": 6}, {"command": 0, "file": 3, "line": 103, "parent": 6}, {"command": 0, "file": 3, "line": 103, "parent": 6}, {"command": 0, "file": 3, "line": 103, "parent": 6}, {"command": 3, "file": 4, "line": 48, "parent": 4}, {"file": 5, "parent": 19}, {"command": 0, "file": 5, "line": 185, "parent": 20}, {"command": 7, "file": 5, "line": 193, "parent": 20}, {"command": 6, "file": 8, "line": 35, "parent": 22}, {"command": 5, "file": 7, "line": 25, "parent": 23}, {"command": 0, "file": 6, "line": 70, "parent": 24}, {"command": 0, "file": 6, "line": 87, "parent": 24}, {"command": 0, "file": 5, "line": 200, "parent": 20}, {"command": 3, "file": 4, "line": 48, "parent": 4}, {"file": 9, "parent": 28}, {"command": 0, "file": 9, "line": 167, "parent": 29}, {"command": 0, "file": 9, "line": 183, "parent": 29}, {"command": 3, "file": 4, "line": 48, "parent": 4}, {"file": 10, "parent": 32}, {"command": 0, "file": 10, "line": 163, "parent": 33}, {"command": 0, "file": 10, "line": 168, "parent": 33}, {"command": 3, "file": 4, "line": 48, "parent": 4}, {"file": 11, "parent": 36}, {"command": 0, "file": 11, "line": 157, "parent": 37}, {"command": 3, "file": 4, "line": 48, "parent": 4}, {"file": 12, "parent": 39}, {"command": 0, "file": 12, "line": 174, "parent": 40}, {"command": 3, "file": 4, "line": 48, "parent": 4}, {"file": 13, "parent": 42}, {"command": 0, "file": 13, "line": 186, "parent": 43}, {"command": 0, "file": 13, "line": 202, "parent": 43}, {"command": 3, "file": 4, "line": 48, "parent": 4}, {"file": 14, "parent": 46}, {"command": 0, "file": 14, "line": 157, "parent": 47}, {"command": 0, "file": 14, "line": 163, "parent": 47}, {"command": 3, "file": 4, "line": 48, "parent": 4}, {"file": 15, "parent": 50}, {"command": 0, "file": 15, "line": 157, "parent": 51}, {"command": 3, "file": 4, "line": 48, "parent": 4}, {"file": 18, "parent": 53}, {"command": 9, "file": 18, "line": 123, "parent": 54}, {"command": 8, "file": 17, "line": 38, "parent": 55}, {"command": 5, "file": 16, "line": 36, "parent": 56}, {"command": 0, "file": 6, "line": 70, "parent": 57}, {"command": 0, "file": 6, "line": 87, "parent": 57}, {"command": 10, "file": 17, "line": 39, "parent": 55}, {"command": 0, "file": 17, "line": 157, "parent": 60}, {"command": 0, "file": 17, "line": 184, "parent": 60}, {"command": 0, "file": 17, "line": 194, "parent": 60}, {"command": 0, "file": 18, "line": 226, "parent": 54}, {"command": 0, "file": 18, "line": 226, "parent": 54}, {"command": 0, "file": 18, "line": 226, "parent": 54}, {"command": 0, "file": 18, "line": 240, "parent": 54}, {"command": 0, "file": 1, "line": 303, "parent": 1}, {"command": 0, "file": 1, "line": 303, "parent": 1}, {"command": 0, "file": 1, "line": 303, "parent": 1}, {"command": 0, "file": 1, "line": 303, "parent": 1}, {"command": 0, "file": 1, "line": 303, "parent": 1}, {"command": 0, "file": 1, "line": 303, "parent": 1}, {"command": 0, "file": 1, "line": 303, "parent": 1}, {"command": 0, "file": 1, "line": 303, "parent": 1}, {"command": 0, "file": 1, "line": 303, "parent": 1}, {"command": 0, "file": 1, "line": 303, "parent": 1}, {"command": 0, "file": 1, "line": 303, "parent": 1}, {"command": 0, "file": 1, "line": 303, "parent": 1}, {"command": 0, "file": 1, "line": 314, "parent": 1}, {"command": 0, "file": 1, "line": 314, "parent": 1}, {"command": 0, "file": 1, "line": 314, "parent": 1}, {"command": 0, "file": 1, "line": 314, "parent": 1}, {"command": 0, "file": 1, "line": 314, "parent": 1}, {"command": 0, "file": 1, "line": 314, "parent": 1}, {"command": 0, "file": 1, "line": 314, "parent": 1}, {"command": 0, "file": 1, "line": 314, "parent": 1}, {"command": 0, "file": 1, "line": 314, "parent": 1}, {"command": 0, "file": 1, "line": 314, "parent": 1}, {"command": 0, "file": 1, "line": 314, "parent": 1}, {"command": 0, "file": 1, "line": 314, "parent": 1}, {"command": 12, "file": 2, "line": 45, "parent": 0}, {"command": 4, "file": 20, "line": 66, "parent": 92}, {"command": 3, "file": 4, "line": 48, "parent": 93}, {"file": 19, "parent": 94}, {"command": 11, "file": 19, "line": 47, "parent": 95}, {"command": 1, "file": 19, "line": 29, "parent": 96}, {"command": 0, "file": 0, "line": 105, "parent": 97}, {"command": 13, "file": 19, "line": 48, "parent": 95}, {"command": 1, "file": 19, "line": 43, "parent": 99}, {"command": 0, "file": 0, "line": 105, "parent": 100}, {"command": 3, "file": 4, "line": 48, "parent": 93}, {"file": 21, "parent": 102}, {"command": 5, "file": 21, "line": 20, "parent": 103}, {"command": 0, "file": 6, "line": 70, "parent": 104}, {"command": 0, "file": 6, "line": 87, "parent": 104}, {"command": 0, "file": 6, "line": 70, "parent": 104}, {"command": 0, "file": 6, "line": 87, "parent": 104}, {"command": 14, "file": 21, "line": 26, "parent": 103}, {"command": 0, "file": 22, "line": 91, "parent": 109}, {"command": 0, "file": 22, "line": 91, "parent": 109}, {"command": 0, "file": 22, "line": 91, "parent": 109}, {"command": 0, "file": 22, "line": 107, "parent": 109}, {"command": 0, "file": 22, "line": 120, "parent": 109}, {"command": 3, "file": 4, "line": 48, "parent": 93}, {"file": 24, "parent": 115}, {"command": 15, "file": 24, "line": 16, "parent": 116}, {"command": 1, "file": 23, "line": 29, "parent": 117}, {"command": 0, "file": 0, "line": 105, "parent": 118}, {"command": 3, "file": 4, "line": 48, "parent": 93}, {"file": 25, "parent": 120}, {"command": 0, "file": 25, "line": 28, "parent": 121}, {"command": 0, "file": 25, "line": 28, "parent": 121}, {"command": 0, "file": 25, "line": 28, "parent": 121}, {"command": 0, "file": 25, "line": 28, "parent": 121}, {"command": 0, "file": 25, "line": 28, "parent": 121}, {"command": 0, "file": 25, "line": 28, "parent": 121}, {"command": 0, "file": 25, "line": 28, "parent": 121}, {"command": 0, "file": 25, "line": 28, "parent": 121}, {"command": 0, "file": 25, "line": 28, "parent": 121}, {"command": 16, "file": 20, "line": 68, "parent": 92}, {"command": 0, "file": 20, "line": 122, "parent": 131}, {"command": 0, "file": 20, "line": 122, "parent": 131}, {"command": 0, "file": 20, "line": 122, "parent": 131}, {"command": 0, "file": 20, "line": 122, "parent": 131}, {"command": 0, "file": 20, "line": 122, "parent": 131}, {"command": 0, "file": 20, "line": 122, "parent": 131}, {"command": 0, "file": 20, "line": 122, "parent": 131}, {"command": 0, "file": 20, "line": 150, "parent": 131}, {"command": 0, "file": 20, "line": 157, "parent": 131}]}, "installers": [{"backtrace": 3, "component": "Unspecified", "destination": "share/ament_index/resource_index/rosidl_interfaces", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera_msgs/ament_cmake_index/share/ament_index/resource_index/rosidl_interfaces/orbbec_camera_msgs"], "type": "file"}, {"backtrace": 7, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/msg", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_type_description/orbbec_camera_msgs/msg/DeviceInfo.json"], "type": "file"}, {"backtrace": 8, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/msg", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_type_description/orbbec_camera_msgs/msg/Extrinsics.json"], "type": "file"}, {"backtrace": 9, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/msg", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_type_description/orbbec_camera_msgs/msg/Metadata.json"], "type": "file"}, {"backtrace": 10, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/msg", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_type_description/orbbec_camera_msgs/msg/IMUInfo.json"], "type": "file"}, {"backtrace": 11, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/msg", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_type_description/orbbec_camera_msgs/msg/RGBD.json"], "type": "file"}, {"backtrace": 12, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/srv", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_type_description/orbbec_camera_msgs/srv/GetBool.json"], "type": "file"}, {"backtrace": 13, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/srv", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_type_description/orbbec_camera_msgs/srv/GetDeviceInfo.json"], "type": "file"}, {"backtrace": 14, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/srv", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_type_description/orbbec_camera_msgs/srv/GetCameraInfo.json"], "type": "file"}, {"backtrace": 15, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/srv", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_type_description/orbbec_camera_msgs/srv/GetInt32.json"], "type": "file"}, {"backtrace": 16, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/srv", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_type_description/orbbec_camera_msgs/srv/GetString.json"], "type": "file"}, {"backtrace": 17, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/srv", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_type_description/orbbec_camera_msgs/srv/SetInt32.json"], "type": "file"}, {"backtrace": 18, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/srv", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_type_description/orbbec_camera_msgs/srv/SetString.json"], "type": "file"}, {"backtrace": 21, "component": "Unspecified", "destination": "include/orbbec_camera_msgs/orbbec_camera_msgs", "paths": [{"from": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_c/orbbec_camera_msgs", "to": "."}], "type": "directory"}, {"backtrace": 25, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/environment", "paths": ["/opt/ros/jazzy/lib/python3.12/site-packages/ament_package/template/environment_hook/library_path.sh"], "type": "file"}, {"backtrace": 26, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/environment", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera_msgs/ament_cmake_environment_hooks/library_path.dsv"], "type": "file"}, {"backtrace": 27, "component": "Unspecified", "destination": "lib", "paths": ["lib<PERSON><PERSON><PERSON>_camera_msgs__rosidl_generator_c.so"], "targetId": "orbbec_camera_msgs__rosidl_generator_c::@6890427a1f51a3e7e1df", "targetIndex": 5, "type": "target"}, {"backtrace": 30, "component": "Unspecified", "destination": "include/orbbec_camera_msgs/orbbec_camera_msgs", "paths": [{"from": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_fastrtps_c/orbbec_camera_msgs", "to": "."}], "type": "directory"}, {"backtrace": 31, "component": "Unspecified", "destination": "lib", "paths": ["libor<PERSON><PERSON>_camera_msgs__rosidl_typesupport_fastrtps_c.so"], "targetId": "orbbec_camera_msgs__rosidl_typesupport_fastrtps_c::@6890427a1f51a3e7e1df", "targetIndex": 10, "type": "target"}, {"backtrace": 34, "component": "Unspecified", "destination": "include/orbbec_camera_msgs/orbbec_camera_msgs", "paths": [{"from": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_introspection_c/orbbec_camera_msgs", "to": "."}], "type": "directory"}, {"backtrace": 35, "component": "Unspecified", "destination": "lib", "paths": ["lib<PERSON><PERSON><PERSON>_camera_msgs__rosidl_typesupport_introspection_c.so"], "targetId": "orbbec_camera_msgs__rosidl_typesupport_introspection_c::@6890427a1f51a3e7e1df", "targetIndex": 12, "type": "target"}, {"backtrace": 38, "component": "Unspecified", "destination": "lib", "paths": ["libor<PERSON><PERSON>_camera_msgs__rosidl_typesupport_c.so"], "targetId": "orbbec_camera_msgs__rosidl_typesupport_c::@6890427a1f51a3e7e1df", "targetIndex": 8, "type": "target"}, {"backtrace": 41, "component": "Unspecified", "destination": "include/orbbec_camera_msgs/orbbec_camera_msgs", "paths": [{"from": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_cpp/orbbec_camera_msgs", "to": "."}], "type": "directory"}, {"backtrace": 44, "component": "Unspecified", "destination": "include/orbbec_camera_msgs/orbbec_camera_msgs", "paths": [{"from": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_fastrtps_cpp/orbbec_camera_msgs", "to": "."}], "type": "directory"}, {"backtrace": 45, "component": "Unspecified", "destination": "lib", "paths": ["libor<PERSON><PERSON>_camera_msgs__rosidl_typesupport_fastrtps_cpp.so"], "targetId": "orbbec_camera_msgs__rosidl_typesupport_fastrtps_cpp::@6890427a1f51a3e7e1df", "targetIndex": 11, "type": "target"}, {"backtrace": 48, "component": "Unspecified", "destination": "include/orbbec_camera_msgs/orbbec_camera_msgs", "paths": [{"from": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_introspection_cpp/orbbec_camera_msgs", "to": "."}], "type": "directory"}, {"backtrace": 49, "component": "Unspecified", "destination": "lib", "paths": ["lib<PERSON><PERSON><PERSON>_camera_msgs__rosidl_typesupport_introspection_cpp.so"], "targetId": "orbbec_camera_msgs__rosidl_typesupport_introspection_cpp::@6890427a1f51a3e7e1df", "targetIndex": 13, "type": "target"}, {"backtrace": 52, "component": "Unspecified", "destination": "lib", "paths": ["libor<PERSON><PERSON>_camera_msgs__rosidl_typesupport_cpp.so"], "targetId": "orbbec_camera_msgs__rosidl_typesupport_cpp::@6890427a1f51a3e7e1df", "targetIndex": 9, "type": "target"}, {"backtrace": 58, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/environment", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera_msgs/ament_cmake_environment_hooks/pythonpath.sh"], "type": "file"}, {"backtrace": 59, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/environment", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera_msgs/ament_cmake_environment_hooks/pythonpath.dsv"], "type": "file"}, {"backtrace": 61, "component": "Unspecified", "destination": "lib/python3.12/site-packages/orbbec_camera_msgs-1.2.2-py3.12.egg-info", "paths": [{"from": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/ament_cmake_python/orbbec_camera_msgs/orbbec_camera_msgs.egg-info", "to": "."}], "type": "directory"}, {"backtrace": 62, "component": "Unspecified", "destination": "lib/python3.12/site-packages/orbbec_camera_msgs", "paths": [{"from": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_py/orbbec_camera_msgs", "to": "."}], "type": "directory"}, {"backtrace": 63, "component": "Unspecified", "type": "code"}, {"backtrace": 64, "component": "Unspecified", "destination": "lib/python3.12/site-packages/orbbec_camera_msgs", "paths": ["rosidl_generator_py/orbbec_camera_msgs/orbbec_camera_msgs_s__rosidl_typesupport_fastrtps_c.so"], "targetId": "orbbec_camera_msgs_s__rosidl_typesupport_fastrtps_c::@6890427a1f51a3e7e1df", "targetIndex": 15, "type": "target"}, {"backtrace": 64, "component": "Unspecified", "cxxModuleBmiTarget": {"id": "orbbec_camera_msgs_s__rosidl_typesupport_fastrtps_c::@6890427a1f51a3e7e1df", "index": 15}, "destination": "lib/python3.12/site-packages/orbbec_camera_msgs", "type": "cxxModuleBmi"}, {"backtrace": 65, "component": "Unspecified", "destination": "lib/python3.12/site-packages/orbbec_camera_msgs", "paths": ["rosidl_generator_py/orbbec_camera_msgs/orbbec_camera_msgs_s__rosidl_typesupport_introspection_c.so"], "targetId": "orbbec_camera_msgs_s__rosidl_typesupport_introspection_c::@6890427a1f51a3e7e1df", "targetIndex": 16, "type": "target"}, {"backtrace": 65, "component": "Unspecified", "cxxModuleBmiTarget": {"id": "orbbec_camera_msgs_s__rosidl_typesupport_introspection_c::@6890427a1f51a3e7e1df", "index": 16}, "destination": "lib/python3.12/site-packages/orbbec_camera_msgs", "type": "cxxModuleBmi"}, {"backtrace": 66, "component": "Unspecified", "destination": "lib/python3.12/site-packages/orbbec_camera_msgs", "paths": ["rosidl_generator_py/orbbec_camera_msgs/orbbec_camera_msgs_s__rosidl_typesupport_c.so"], "targetId": "orbbec_camera_msgs_s__rosidl_typesupport_c::@6890427a1f51a3e7e1df", "targetIndex": 14, "type": "target"}, {"backtrace": 66, "component": "Unspecified", "cxxModuleBmiTarget": {"id": "orbbec_camera_msgs_s__rosidl_typesupport_c::@6890427a1f51a3e7e1df", "index": 14}, "destination": "lib/python3.12/site-packages/orbbec_camera_msgs", "type": "cxxModuleBmi"}, {"backtrace": 67, "component": "Unspecified", "destination": "lib", "paths": ["lib<PERSON><PERSON><PERSON>_camera_msgs__rosidl_generator_py.so"], "targetId": "orbbec_camera_msgs__rosidl_generator_py::@6890427a1f51a3e7e1df", "targetIndex": 6, "type": "target"}, {"backtrace": 68, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/msg", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_adapter/orbbec_camera_msgs/msg/DeviceInfo.idl"], "type": "file"}, {"backtrace": 69, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/msg", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_adapter/orbbec_camera_msgs/msg/Extrinsics.idl"], "type": "file"}, {"backtrace": 70, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/msg", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_adapter/orbbec_camera_msgs/msg/Metadata.idl"], "type": "file"}, {"backtrace": 71, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/msg", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_adapter/orbbec_camera_msgs/msg/IMUInfo.idl"], "type": "file"}, {"backtrace": 72, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/msg", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_adapter/orbbec_camera_msgs/msg/RGBD.idl"], "type": "file"}, {"backtrace": 73, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/srv", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_adapter/orbbec_camera_msgs/srv/GetBool.idl"], "type": "file"}, {"backtrace": 74, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/srv", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_adapter/orbbec_camera_msgs/srv/GetDeviceInfo.idl"], "type": "file"}, {"backtrace": 75, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/srv", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_adapter/orbbec_camera_msgs/srv/GetCameraInfo.idl"], "type": "file"}, {"backtrace": 76, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/srv", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_adapter/orbbec_camera_msgs/srv/GetInt32.idl"], "type": "file"}, {"backtrace": 77, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/srv", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_adapter/orbbec_camera_msgs/srv/GetString.idl"], "type": "file"}, {"backtrace": 78, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/srv", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_adapter/orbbec_camera_msgs/srv/SetInt32.idl"], "type": "file"}, {"backtrace": 79, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/srv", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_adapter/orbbec_camera_msgs/srv/SetString.idl"], "type": "file"}, {"backtrace": 80, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/msg", "paths": ["msg/DeviceInfo.msg"], "type": "file"}, {"backtrace": 81, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/msg", "paths": ["msg/Extrinsics.msg"], "type": "file"}, {"backtrace": 82, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/msg", "paths": ["msg/Metadata.msg"], "type": "file"}, {"backtrace": 83, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/msg", "paths": ["msg/IMUInfo.msg"], "type": "file"}, {"backtrace": 84, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/msg", "paths": ["msg/RGBD.msg"], "type": "file"}, {"backtrace": 85, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/srv", "paths": ["srv/GetBool.srv"], "type": "file"}, {"backtrace": 86, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/srv", "paths": ["srv/GetDeviceInfo.srv"], "type": "file"}, {"backtrace": 87, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/srv", "paths": ["srv/GetCameraInfo.srv"], "type": "file"}, {"backtrace": 88, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/srv", "paths": ["srv/GetInt32.srv"], "type": "file"}, {"backtrace": 89, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/srv", "paths": ["srv/GetString.srv"], "type": "file"}, {"backtrace": 90, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/srv", "paths": ["srv/SetInt32.srv"], "type": "file"}, {"backtrace": 91, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/srv", "paths": ["srv/SetString.srv"], "type": "file"}, {"backtrace": 98, "component": "Unspecified", "destination": "share/ament_index/resource_index/package_run_dependencies", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera_msgs/ament_cmake_index/share/ament_index/resource_index/package_run_dependencies/orbbec_camera_msgs"], "type": "file"}, {"backtrace": 101, "component": "Unspecified", "destination": "share/ament_index/resource_index/parent_prefix_path", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera_msgs/ament_cmake_index/share/ament_index/resource_index/parent_prefix_path/orbbec_camera_msgs"], "type": "file"}, {"backtrace": 105, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/environment", "paths": ["/opt/ros/jazzy/share/ament_cmake_core/cmake/environment_hooks/environment/ament_prefix_path.sh"], "type": "file"}, {"backtrace": 106, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/environment", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera_msgs/ament_cmake_environment_hooks/ament_prefix_path.dsv"], "type": "file"}, {"backtrace": 107, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/environment", "paths": ["/opt/ros/jazzy/share/ament_cmake_core/cmake/environment_hooks/environment/path.sh"], "type": "file"}, {"backtrace": 108, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/environment", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera_msgs/ament_cmake_environment_hooks/path.dsv"], "type": "file"}, {"backtrace": 110, "component": "Unspecified", "destination": "share/orbbec_camera_msgs", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera_msgs/ament_cmake_environment_hooks/local_setup.bash"], "type": "file"}, {"backtrace": 111, "component": "Unspecified", "destination": "share/orbbec_camera_msgs", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera_msgs/ament_cmake_environment_hooks/local_setup.sh"], "type": "file"}, {"backtrace": 112, "component": "Unspecified", "destination": "share/orbbec_camera_msgs", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera_msgs/ament_cmake_environment_hooks/local_setup.zsh"], "type": "file"}, {"backtrace": 113, "component": "Unspecified", "destination": "share/orbbec_camera_msgs", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera_msgs/ament_cmake_environment_hooks/local_setup.dsv"], "type": "file"}, {"backtrace": 114, "component": "Unspecified", "destination": "share/orbbec_camera_msgs", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera_msgs/ament_cmake_environment_hooks/package.dsv"], "type": "file"}, {"backtrace": 119, "component": "Unspecified", "destination": "share/ament_index/resource_index/packages", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera_msgs/ament_cmake_index/share/ament_index/resource_index/packages/orbbec_camera_msgs"], "type": "file"}, {"backtrace": 122, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/cmake", "exportName": "export_orbbec_camera_msgs__rosidl_generator_c", "exportTargets": [{"id": "orbbec_camera_msgs__rosidl_generator_c::@6890427a1f51a3e7e1df", "index": 5}], "paths": ["CMakeFiles/Export/173b3c82279e91426e57c0d609726c0e/export_orbbec_camera_msgs__rosidl_generator_cExport.cmake"], "type": "export"}, {"backtrace": 123, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/cmake", "exportName": "export_orbbec_camera_msgs__rosidl_typesupport_fastrtps_c", "exportTargets": [{"id": "orbbec_camera_msgs__rosidl_typesupport_fastrtps_c::@6890427a1f51a3e7e1df", "index": 10}], "paths": ["CMakeFiles/Export/173b3c82279e91426e57c0d609726c0e/export_orbbec_camera_msgs__rosidl_typesupport_fastrtps_cExport.cmake"], "type": "export"}, {"backtrace": 124, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/cmake", "exportName": "orbbec_camera_msgs__rosidl_typesupport_introspection_c", "exportTargets": [{"id": "orbbec_camera_msgs__rosidl_typesupport_introspection_c::@6890427a1f51a3e7e1df", "index": 12}], "paths": ["CMakeFiles/Export/173b3c82279e91426e57c0d609726c0e/orbbec_camera_msgs__rosidl_typesupport_introspection_cExport.cmake"], "type": "export"}, {"backtrace": 125, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/cmake", "exportName": "orbbec_camera_msgs__rosidl_typesupport_c", "exportTargets": [{"id": "orbbec_camera_msgs__rosidl_typesupport_c::@6890427a1f51a3e7e1df", "index": 8}], "paths": ["CMakeFiles/Export/173b3c82279e91426e57c0d609726c0e/orbbec_camera_msgs__rosidl_typesupport_cExport.cmake"], "type": "export"}, {"backtrace": 126, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/cmake", "exportName": "export_orbbec_camera_msgs__rosidl_generator_cpp", "exportTargets": [{"id": "orbbec_camera_msgs__rosidl_generator_cpp::@6890427a1f51a3e7e1df", "index": 0}], "paths": ["CMakeFiles/Export/173b3c82279e91426e57c0d609726c0e/export_orbbec_camera_msgs__rosidl_generator_cppExport.cmake"], "type": "export"}, {"backtrace": 127, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/cmake", "exportName": "export_orbbec_camera_msgs__rosidl_typesupport_fastrtps_cpp", "exportTargets": [{"id": "orbbec_camera_msgs__rosidl_typesupport_fastrtps_cpp::@6890427a1f51a3e7e1df", "index": 11}], "paths": ["CMakeFiles/Export/173b3c82279e91426e57c0d609726c0e/export_orbbec_camera_msgs__rosidl_typesupport_fastrtps_cppExport.cmake"], "type": "export"}, {"backtrace": 128, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/cmake", "exportName": "orbbec_camera_msgs__rosidl_typesupport_introspection_cpp", "exportTargets": [{"id": "orbbec_camera_msgs__rosidl_typesupport_introspection_cpp::@6890427a1f51a3e7e1df", "index": 13}], "paths": ["CMakeFiles/Export/173b3c82279e91426e57c0d609726c0e/orbbec_camera_msgs__rosidl_typesupport_introspection_cppExport.cmake"], "type": "export"}, {"backtrace": 129, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/cmake", "exportName": "orbbec_camera_msgs__rosidl_typesupport_cpp", "exportTargets": [{"id": "orbbec_camera_msgs__rosidl_typesupport_cpp::@6890427a1f51a3e7e1df", "index": 9}], "paths": ["CMakeFiles/Export/173b3c82279e91426e57c0d609726c0e/orbbec_camera_msgs__rosidl_typesupport_cppExport.cmake"], "type": "export"}, {"backtrace": 130, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/cmake", "exportName": "export_orbbec_camera_msgs__rosidl_generator_py", "exportTargets": [{"id": "orbbec_camera_msgs__rosidl_generator_py::@6890427a1f51a3e7e1df", "index": 6}], "paths": ["CMakeFiles/Export/173b3c82279e91426e57c0d609726c0e/export_orbbec_camera_msgs__rosidl_generator_pyExport.cmake"], "type": "export"}, {"backtrace": 132, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/cmake", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_cmake/rosidl_cmake-extras.cmake"], "type": "file"}, {"backtrace": 133, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/cmake", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera_msgs/ament_cmake_export_dependencies/ament_cmake_export_dependencies-extras.cmake"], "type": "file"}, {"backtrace": 134, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/cmake", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera_msgs/ament_cmake_export_include_directories/ament_cmake_export_include_directories-extras.cmake"], "type": "file"}, {"backtrace": 135, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/cmake", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera_msgs/ament_cmake_export_libraries/ament_cmake_export_libraries-extras.cmake"], "type": "file"}, {"backtrace": 136, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/cmake", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera_msgs/ament_cmake_export_targets/ament_cmake_export_targets-extras.cmake"], "type": "file"}, {"backtrace": 137, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/cmake", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_cmake/rosidl_cmake_export_typesupport_targets-extras.cmake"], "type": "file"}, {"backtrace": 138, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/cmake", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_cmake/rosidl_cmake_export_typesupport_libraries-extras.cmake"], "type": "file"}, {"backtrace": 139, "component": "Unspecified", "destination": "share/orbbec_camera_msgs/cmake", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera_msgs/ament_cmake_core/orbbec_camera_msgsConfig.cmake", "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/ament_cmake_core/orbbec_camera_msgsConfig-version.cmake"], "type": "file"}, {"backtrace": 140, "component": "Unspecified", "destination": "share/orbbec_camera_msgs", "paths": ["package.xml"], "type": "file"}], "paths": {"build": ".", "source": "."}}