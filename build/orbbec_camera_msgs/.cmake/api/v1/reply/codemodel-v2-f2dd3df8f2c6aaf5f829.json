{"configurations": [{"directories": [{"build": ".", "childIndexes": [1], "hasInstallRule": true, "jsonFile": "directory-.-be022ebb4655f95c60b8.json", "minimumCMakeVersion": {"string": "3.20"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18]}, {"build": "orbbec_camera_msgs__py", "jsonFile": "directory-orbbec_camera_msgs__py-1ffb8a425663baebd2a9.json", "minimumCMakeVersion": {"string": "3.20"}, "parentIndex": 0, "projectIndex": 0, "source": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/orbbec_camera_msgs__py", "targetIndexes": [4]}], "name": "", "projects": [{"directoryIndexes": [0, 1], "name": "orbbec_camera_msgs", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18]}], "targets": [{"directoryIndex": 0, "id": "ament_cmake_python_build_orbbec_camera_msgs_egg::@6890427a1f51a3e7e1df", "jsonFile": "target-ament_cmake_python_build_orbbec_camera_msgs_egg-c64c17e72dc304e15af7.json", "name": "ament_cmake_python_build_orbbec_camera_msgs_egg", "projectIndex": 0}, {"directoryIndex": 0, "id": "ament_cmake_python_copy_orbbec_camera_msgs::@6890427a1f51a3e7e1df", "jsonFile": "target-ament_cmake_python_copy_orbbec_camera_msgs-ad29f95aa6782724b23e.json", "name": "ament_cmake_python_copy_orbbec_camera_msgs", "projectIndex": 0}, {"directoryIndex": 0, "id": "orbbec_camera_msgs::@6890427a1f51a3e7e1df", "jsonFile": "target-orbbec_camera_msgs-5a8b96410247feda2da8.json", "name": "orbbec_camera_msgs", "projectIndex": 0}, {"directoryIndex": 0, "id": "orbbec_camera_msgs__cpp::@6890427a1f51a3e7e1df", "jsonFile": "target-orbbec_camera_msgs__cpp-fd9bd1e97c05feb2ee06.json", "name": "orbbec_camera_msgs__cpp", "projectIndex": 0}, {"directoryIndex": 1, "id": "orbbec_camera_msgs__py::@b99f976d5a7686d60dd0", "jsonFile": "target-orbbec_camera_msgs__py-070f6c507f4964066ce4.json", "name": "orbbec_camera_msgs__py", "projectIndex": 0}, {"directoryIndex": 0, "id": "orbbec_camera_msgs__rosidl_generator_c::@6890427a1f51a3e7e1df", "jsonFile": "target-orbbec_camera_msgs__rosidl_generator_c-92e49f9f42c61201a0b3.json", "name": "orbbec_camera_msgs__rosidl_generator_c", "projectIndex": 0}, {"directoryIndex": 0, "id": "orbbec_camera_msgs__rosidl_generator_py::@6890427a1f51a3e7e1df", "jsonFile": "target-orbbec_camera_msgs__rosidl_generator_py-055f69bdfb57e23a5660.json", "name": "orbbec_camera_msgs__rosidl_generator_py", "projectIndex": 0}, {"directoryIndex": 0, "id": "orbbec_camera_msgs__rosidl_generator_type_description::@6890427a1f51a3e7e1df", "jsonFile": "target-orbbec_camera_msgs__rosidl_generator_type_description-f322c0ed049fb381f678.json", "name": "orbbec_camera_msgs__rosidl_generator_type_description", "projectIndex": 0}, {"directoryIndex": 0, "id": "orbbec_camera_msgs__rosidl_typesupport_c::@6890427a1f51a3e7e1df", "jsonFile": "target-orbbec_camera_msgs__rosidl_typesupport_c-abab6b76b8aa25aa196e.json", "name": "orbbec_camera_msgs__rosidl_typesupport_c", "projectIndex": 0}, {"directoryIndex": 0, "id": "orbbec_camera_msgs__rosidl_typesupport_cpp::@6890427a1f51a3e7e1df", "jsonFile": "target-orbbec_camera_msgs__rosidl_typesupport_cpp-2ecb0a924bc6dc6939e1.json", "name": "orbbec_camera_msgs__rosidl_typesupport_cpp", "projectIndex": 0}, {"directoryIndex": 0, "id": "orbbec_camera_msgs__rosidl_typesupport_fastrtps_c::@6890427a1f51a3e7e1df", "jsonFile": "target-orbbec_camera_msgs__rosidl_typesupport_fastrtps_c-bd0d99991802a7b7e726.json", "name": "orbbec_camera_msgs__rosidl_typesupport_fastrtps_c", "projectIndex": 0}, {"directoryIndex": 0, "id": "orbbec_camera_msgs__rosidl_typesupport_fastrtps_cpp::@6890427a1f51a3e7e1df", "jsonFile": "target-orbbec_camera_msgs__rosidl_typesupport_fastrtps_cpp-1c0d760835c2d3404a14.json", "name": "orbbec_camera_msgs__rosidl_typesupport_fastrtps_cpp", "projectIndex": 0}, {"directoryIndex": 0, "id": "orbbec_camera_msgs__rosidl_typesupport_introspection_c::@6890427a1f51a3e7e1df", "jsonFile": "target-orbbec_camera_msgs__rosidl_typesupport_introspection_c-28f5cc80cc5a44d3b9f6.json", "name": "orbbec_camera_msgs__rosidl_typesupport_introspection_c", "projectIndex": 0}, {"directoryIndex": 0, "id": "orbbec_camera_msgs__rosidl_typesupport_introspection_cpp::@6890427a1f51a3e7e1df", "jsonFile": "target-orbbec_camera_msgs__rosidl_typesupport_introspection_cpp-5004fec57f8ae886a011.json", "name": "orbbec_camera_msgs__rosidl_typesupport_introspection_cpp", "projectIndex": 0}, {"directoryIndex": 0, "id": "orbbec_camera_msgs_s__rosidl_typesupport_c::@6890427a1f51a3e7e1df", "jsonFile": "target-orbbec_camera_msgs_s__rosidl_typesupport_c-adbc82ce295926d988a6.json", "name": "orbbec_camera_msgs_s__rosidl_typesupport_c", "projectIndex": 0}, {"directoryIndex": 0, "id": "orbbec_camera_msgs_s__rosidl_typesupport_fastrtps_c::@6890427a1f51a3e7e1df", "jsonFile": "target-orbbec_camera_msgs_s__rosidl_typesupport_fastrtps_c-f27f096c6fa0a55d2dcf.json", "name": "orbbec_camera_msgs_s__rosidl_typesupport_fastrtps_c", "projectIndex": 0}, {"directoryIndex": 0, "id": "orbbec_camera_msgs_s__rosidl_typesupport_introspection_c::@6890427a1f51a3e7e1df", "jsonFile": "target-orbbec_camera_msgs_s__rosidl_typesupport_introspection_c-ec487988b3230c8946d6.json", "name": "orbbe<PERSON>_camera_msgs_s__rosidl_typesupport_introspection_c", "projectIndex": 0}, {"directoryIndex": 0, "id": "orbbec_camera_msgs_uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-orbbec_camera_msgs_uninstall-b3be7f4a4aff5fba5d72.json", "name": "orbbec_camera_msgs_uninstall", "projectIndex": 0}, {"directoryIndex": 0, "id": "uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-uninstall-c561ac0841895996660d.json", "name": "uninstall", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs", "source": "/home/<USER>/ws_ros2/src/Sensors_ROS2/OrbbecSDK_ROS2/orbbec_camera_msgs"}, "version": {"major": 2, "minor": 6}}