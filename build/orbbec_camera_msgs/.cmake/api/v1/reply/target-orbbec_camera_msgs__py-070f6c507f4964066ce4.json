{"backtrace": 1, "backtraceGraph": {"commands": ["add_custom_target"], "files": ["/home/<USER>/ws_ros2/build/orbbec_camera_msgs/orbbec_camera_msgs__py/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 36, "parent": 0}]}, "dependencies": [{"id": "orbbec_camera_msgs::@6890427a1f51a3e7e1df"}], "id": "orbbec_camera_msgs__py::@b99f976d5a7686d60dd0", "name": "orbbec_camera_msgs__py", "paths": {"build": "orbbec_camera_msgs__py", "source": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/orbbec_camera_msgs__py"}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1, 2]}], "sources": [{"backtrace": 1, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/orbbec_camera_msgs__py/CMakeFiles/orbbec_camera_msgs__py", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/orbbec_camera_msgs__py/CMakeFiles/orbbec_camera_msgs__py.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_py/orbbec_camera_msgs/_orbbec_camera_msgs_s.ep.rosidl_typesupport_fastrtps_c.c.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}