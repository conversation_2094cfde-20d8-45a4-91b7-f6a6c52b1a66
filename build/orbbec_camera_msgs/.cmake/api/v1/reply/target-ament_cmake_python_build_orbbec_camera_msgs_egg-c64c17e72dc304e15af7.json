{"backtrace": 7, "backtraceGraph": {"commands": ["add_custom_target", "_ament_cmake_python_install_package", "ament_python_install_package", "include", "ament_execute_extensions", "rosidl_generate_interfaces"], "files": ["/opt/ros/jazzy/share/ament_cmake_python/cmake/ament_python_install_package.cmake", "/opt/ros/jazzy/share/rosidl_generator_py/cmake/rosidl_generator_py_generate_interfaces.cmake", "/opt/ros/jazzy/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake", "/opt/ros/jazzy/share/rosidl_cmake/cmake/rosidl_generate_interfaces.cmake", "CMakeLists.txt"], "nodes": [{"file": 4}, {"command": 5, "file": 4, "line": 17, "parent": 0}, {"command": 4, "file": 3, "line": 280, "parent": 1}, {"command": 3, "file": 2, "line": 48, "parent": 2}, {"file": 1, "parent": 3}, {"command": 2, "file": 1, "line": 123, "parent": 4}, {"command": 1, "file": 0, "line": 39, "parent": 5}, {"command": 0, "file": 0, "line": 144, "parent": 6}]}, "dependencies": [{"id": "ament_cmake_python_copy_orbbec_camera_msgs::@6890427a1f51a3e7e1df"}], "id": "ament_cmake_python_build_orbbec_camera_msgs_egg::@6890427a1f51a3e7e1df", "name": "ament_cmake_python_build_orbbec_camera_msgs_egg", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1]}], "sources": [{"backtrace": 7, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/CMakeFiles/ament_cmake_python_build_orbbec_camera_msgs_egg", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/CMakeFiles/ament_cmake_python_build_orbbec_camera_msgs_egg.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}