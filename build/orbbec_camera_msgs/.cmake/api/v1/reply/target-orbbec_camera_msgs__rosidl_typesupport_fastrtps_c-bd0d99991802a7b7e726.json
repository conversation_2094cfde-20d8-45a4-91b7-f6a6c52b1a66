{"artifacts": [{"path": "libor<PERSON><PERSON>_camera_msgs__rosidl_typesupport_fastrtps_c.so"}], "backtrace": 5, "backtraceGraph": {"commands": ["add_library", "include", "ament_execute_extensions", "rosidl_generate_interfaces", "install", "target_link_libraries", "set_target_properties", "find_package", "add_compile_options", "add_definitions", "target_include_directories"], "files": ["/opt/ros/jazzy/share/rosidl_typesupport_fastrtps_c/cmake/rosidl_typesupport_fastrtps_c_generate_interfaces.cmake", "/opt/ros/jazzy/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake", "/opt/ros/jazzy/share/rosidl_cmake/cmake/rosidl_generate_interfaces.cmake", "CMakeLists.txt", "/opt/ros/jazzy/share/rosidl_typesupport_fastrtps_cpp/cmake/rosidl_typesupport_fastrtps_cppExport.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_fastrtps_cpp/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_fastrtps_cpp/cmake/rosidl_typesupport_fastrtps_cppConfig.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_cpp/cmake/rosidl_typesupport_cpp-extras.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_cpp/cmake/rosidl_typesupport_cppConfig.cmake", "/opt/ros/jazzy/share/rosidl_core_generators/cmake/rosidl_core_generators-extras.cmake", "/opt/ros/jazzy/share/rosidl_core_generators/cmake/rosidl_core_generatorsConfig.cmake", "/opt/ros/jazzy/share/rosidl_default_generators/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/jazzy/share/rosidl_default_generators/cmake/rosidl_default_generatorsConfig.cmake", "/opt/ros/jazzy/share/rmw/cmake/rmwExport.cmake", "/opt/ros/jazzy/share/rmw/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/jazzy/share/rmw/cmake/rmwConfig.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_fastrtps_cpp/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/jazzy/share/rosidl_generator_c/cmake/rosidl_generator_c_generate_interfaces.cmake", "/opt/ros/jazzy/share/rosidl_runtime_c/cmake/rosidl_runtime_cExport.cmake", "/opt/ros/jazzy/share/rosidl_runtime_c/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/jazzy/share/rosidl_runtime_c/cmake/rosidl_runtime_cConfig.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_fastrtps_c/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_fastrtps_c/cmake/rosidl_typesupport_fastrtps_cConfig.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_c/cmake/rosidl_typesupport_c-extras.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_c/cmake/rosidl_typesupport_cConfig.cmake", "/opt/ros/jazzy/share/rcutils/cmake/rcutilsExport.cmake", "/opt/ros/jazzy/share/rcutils/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/jazzy/share/rcutils/cmake/rcutilsConfig.cmake", "/opt/ros/jazzy/share/rosidl_runtime_c/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/jazzy/share/ament_cmake_ros/cmake/ament_cmake_ros-extras.cmake", "/opt/ros/jazzy/share/ament_cmake_ros/cmake/ament_cmake_rosConfig.cmake"], "nodes": [{"file": 3}, {"command": 3, "file": 3, "line": 17, "parent": 0}, {"command": 2, "file": 2, "line": 280, "parent": 1}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 0, "parent": 3}, {"command": 0, "file": 0, "line": 120, "parent": 4}, {"command": 4, "file": 0, "line": 183, "parent": 4}, {"command": 5, "file": 0, "line": 147, "parent": 4}, {"command": 5, "file": 0, "line": 157, "parent": 4}, {"command": 5, "file": 0, "line": 157, "parent": 4}, {"command": 5, "file": 0, "line": 157, "parent": 4}, {"command": 5, "file": 0, "line": 157, "parent": 4}, {"command": 5, "file": 0, "line": 157, "parent": 4}, {"command": 5, "file": 0, "line": 131, "parent": 4}, {"command": 7, "file": 3, "line": 10, "parent": 0}, {"file": 12, "parent": 14}, {"command": 1, "file": 12, "line": 41, "parent": 15}, {"file": 11, "parent": 16}, {"command": 7, "file": 11, "line": 21, "parent": 17}, {"file": 10, "parent": 18}, {"command": 1, "file": 10, "line": 41, "parent": 19}, {"file": 9, "parent": 20}, {"command": 7, "file": 9, "line": 21, "parent": 21}, {"file": 8, "parent": 22}, {"command": 1, "file": 8, "line": 41, "parent": 23}, {"file": 7, "parent": 24}, {"command": 7, "file": 7, "line": 13, "parent": 25}, {"file": 6, "parent": 26}, {"command": 1, "file": 6, "line": 41, "parent": 27}, {"file": 5, "parent": 28}, {"command": 1, "file": 5, "line": 9, "parent": 29}, {"file": 4, "parent": 30}, {"command": 6, "file": 4, "line": 61, "parent": 31}, {"command": 1, "file": 6, "line": 41, "parent": 27}, {"file": 16, "parent": 33}, {"command": 7, "file": 16, "line": 21, "parent": 34}, {"file": 15, "parent": 35}, {"command": 1, "file": 15, "line": 41, "parent": 36}, {"file": 14, "parent": 37}, {"command": 1, "file": 14, "line": 9, "parent": 38}, {"file": 13, "parent": 39}, {"command": 6, "file": 13, "line": 61, "parent": 40}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 17, "parent": 42}, {"command": 5, "file": 17, "line": 165, "parent": 43}, {"command": 5, "file": 17, "line": 165, "parent": 43}, {"command": 5, "file": 17, "line": 165, "parent": 43}, {"command": 5, "file": 17, "line": 165, "parent": 43}, {"command": 5, "file": 17, "line": 165, "parent": 43}, {"command": 7, "file": 9, "line": 21, "parent": 21}, {"file": 24, "parent": 49}, {"command": 1, "file": 24, "line": 41, "parent": 50}, {"file": 23, "parent": 51}, {"command": 7, "file": 23, "line": 13, "parent": 52}, {"file": 22, "parent": 53}, {"command": 1, "file": 22, "line": 41, "parent": 54}, {"file": 21, "parent": 55}, {"command": 7, "file": 21, "line": 21, "parent": 56}, {"file": 20, "parent": 57}, {"command": 1, "file": 20, "line": 41, "parent": 58}, {"file": 19, "parent": 59}, {"command": 1, "file": 19, "line": 9, "parent": 60}, {"file": 18, "parent": 61}, {"command": 6, "file": 18, "line": 61, "parent": 62}, {"command": 1, "file": 20, "line": 41, "parent": 58}, {"file": 28, "parent": 64}, {"command": 7, "file": 28, "line": 21, "parent": 65}, {"file": 27, "parent": 66}, {"command": 1, "file": 27, "line": 41, "parent": 67}, {"file": 26, "parent": 68}, {"command": 1, "file": 26, "line": 9, "parent": 69}, {"file": 25, "parent": 70}, {"command": 6, "file": 25, "line": 61, "parent": 71}, {"command": 8, "file": 3, "line": 5, "parent": 0}, {"command": 7, "file": 0, "line": 21, "parent": 4}, {"file": 30, "parent": 74}, {"command": 1, "file": 30, "line": 41, "parent": 75}, {"file": 29, "parent": 76}, {"command": 9, "file": 29, "line": 25, "parent": 77}, {"command": 10, "file": 0, "line": 150, "parent": 4}, {"command": 6, "file": 0, "line": 126, "parent": 4}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-std=gnu++17 -fPIC"}, {"backtrace": 73, "fragment": "-Wall"}, {"backtrace": 73, "fragment": "-Wextra"}, {"backtrace": 73, "fragment": "-Wpedantic"}], "defines": [{"backtrace": 13, "define": "FASTCDR_DYN_LINK"}, {"define": "ROSIDL_TYPESUPPORT_FASTRTPS_C_BUILDING_DLL_orbbec_camera_msgs"}, {"backtrace": 78, "define": "ROS_PACKAGE_NAME=\"orbbec_camera_msgs\""}], "includes": [{"backtrace": 79, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_fastrtps_c"}, {"backtrace": 7, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_c"}, {"backtrace": 13, "isSystem": true, "path": "/opt/ros/jazzy/include/fastcdr"}, {"backtrace": 13, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_runtime_c"}, {"backtrace": 13, "isSystem": true, "path": "/opt/ros/jazzy/include/rcutils"}, {"backtrace": 13, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_typesupport_interface"}, {"backtrace": 13, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_runtime_cpp"}, {"backtrace": 13, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_typesupport_fastrtps_cpp"}, {"backtrace": 13, "isSystem": true, "path": "/opt/ros/jazzy/include/rmw"}, {"backtrace": 13, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_dynamic_typesupport"}, {"backtrace": 13, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_typesupport_fastrtps_c"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/jazzy/include/sensor_msgs"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/jazzy/include/builtin_interfaces"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/jazzy/include/geometry_msgs"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/jazzy/include/std_msgs"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/jazzy/include/service_msgs"}], "language": "CXX", "languageStandard": {"backtraces": [80], "standard": "17"}, "sourceIndexes": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23]}], "dependencies": [{"backtrace": 7, "id": "orbbec_camera_msgs__rosidl_generator_c::@6890427a1f51a3e7e1df"}], "id": "orbbec_camera_msgs__rosidl_typesupport_fastrtps_c::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 6, "path": "lib"}, {"backtrace": 6, "path": "lib"}], "prefix": {"path": "/home/<USER>/ws_ros2/install/orbbec_camera_msgs"}}, "link": {"commandFragments": [{"fragment": "", "role": "flags"}, {"fragment": "-Wl,-rpath,/home/<USER>/ws_ros2/build/orbbec_camera_msgs:/opt/ros/jazzy/lib:", "role": "libraries"}, {"backtrace": 7, "fragment": "lib<PERSON><PERSON><PERSON>_camera_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 8, "fragment": "/opt/ros/jazzy/lib/libsensor_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 9, "fragment": "/opt/ros/jazzy/lib/libgeometry_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/jazzy/lib/libstd_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/jazzy/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 13, "fragment": "/opt/ros/jazzy/lib/librosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 32, "fragment": "/opt/ros/jazzy/lib/librmw.so", "role": "libraries"}, {"backtrace": 41, "fragment": "/opt/ros/jazzy/lib/librosidl_dynamic_typesupport.so", "role": "libraries"}, {"backtrace": 13, "fragment": "/opt/ros/jazzy/lib/librosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 13, "fragment": "/opt/ros/jazzy/lib/libfastcdr.so.2.2.5", "role": "libraries"}, {"backtrace": 44, "fragment": "/opt/ros/jazzy/lib/libsensor_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 45, "fragment": "/opt/ros/jazzy/lib/libgeometry_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 46, "fragment": "/opt/ros/jazzy/lib/libstd_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 47, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 48, "fragment": "/opt/ros/jazzy/lib/libbuiltin_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 13, "fragment": "/opt/ros/jazzy/lib/librosidl_runtime_c.so", "role": "libraries"}, {"backtrace": 63, "fragment": "/opt/ros/jazzy/lib/librcutils.so", "role": "libraries"}, {"backtrace": 72, "fragment": "-ldl", "role": "libraries"}], "language": "CXX"}, "name": "orbbec_camera_msgs__rosidl_typesupport_fastrtps_c", "nameOnDisk": "libor<PERSON><PERSON>_camera_msgs__rosidl_typesupport_fastrtps_c.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "<PERSON><PERSON>", "sourceIndexes": [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22]}, {"name": "Source Files", "sourceIndexes": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23]}, {"name": "CMake Rules", "sourceIndexes": [24]}], "sources": [{"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_fastrtps_c/orbbec_camera_msgs/msg/detail/device_info__rosidl_typesupport_fastrtps_c.h", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_fastrtps_c/orbbec_camera_msgs/msg/detail/device_info__type_support_c.cpp", "sourceGroupIndex": 1}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_fastrtps_c/orbbec_camera_msgs/msg/detail/extrinsics__rosidl_typesupport_fastrtps_c.h", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_fastrtps_c/orbbec_camera_msgs/msg/detail/extrinsics__type_support_c.cpp", "sourceGroupIndex": 1}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_fastrtps_c/orbbec_camera_msgs/msg/detail/metadata__rosidl_typesupport_fastrtps_c.h", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_fastrtps_c/orbbec_camera_msgs/msg/detail/metadata__type_support_c.cpp", "sourceGroupIndex": 1}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_fastrtps_c/orbbec_camera_msgs/msg/detail/imu_info__rosidl_typesupport_fastrtps_c.h", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_fastrtps_c/orbbec_camera_msgs/msg/detail/imu_info__type_support_c.cpp", "sourceGroupIndex": 1}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_fastrtps_c/orbbec_camera_msgs/msg/detail/rgbd__rosidl_typesupport_fastrtps_c.h", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_fastrtps_c/orbbec_camera_msgs/msg/detail/rgbd__type_support_c.cpp", "sourceGroupIndex": 1}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_fastrtps_c/orbbec_camera_msgs/srv/detail/get_bool__rosidl_typesupport_fastrtps_c.h", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_fastrtps_c/orbbec_camera_msgs/srv/detail/get_bool__type_support_c.cpp", "sourceGroupIndex": 1}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_fastrtps_c/orbbec_camera_msgs/srv/detail/get_device_info__rosidl_typesupport_fastrtps_c.h", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_fastrtps_c/orbbec_camera_msgs/srv/detail/get_device_info__type_support_c.cpp", "sourceGroupIndex": 1}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_fastrtps_c/orbbec_camera_msgs/srv/detail/get_camera_info__rosidl_typesupport_fastrtps_c.h", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_fastrtps_c/orbbec_camera_msgs/srv/detail/get_camera_info__type_support_c.cpp", "sourceGroupIndex": 1}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_fastrtps_c/orbbec_camera_msgs/srv/detail/get_int32__rosidl_typesupport_fastrtps_c.h", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_fastrtps_c/orbbec_camera_msgs/srv/detail/get_int32__type_support_c.cpp", "sourceGroupIndex": 1}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_fastrtps_c/orbbec_camera_msgs/srv/detail/get_string__rosidl_typesupport_fastrtps_c.h", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_fastrtps_c/orbbec_camera_msgs/srv/detail/get_string__type_support_c.cpp", "sourceGroupIndex": 1}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_fastrtps_c/orbbec_camera_msgs/srv/detail/set_int32__rosidl_typesupport_fastrtps_c.h", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_fastrtps_c/orbbec_camera_msgs/srv/detail/set_int32__type_support_c.cpp", "sourceGroupIndex": 1}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_fastrtps_c/orbbec_camera_msgs/srv/detail/set_string__rosidl_typesupport_fastrtps_c.h", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_fastrtps_c/orbbec_camera_msgs/srv/detail/set_string__type_support_c.cpp", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_fastrtps_c/orbbec_camera_msgs/msg/detail/device_info__rosidl_typesupport_fastrtps_c.h.rule", "sourceGroupIndex": 2}], "type": "SHARED_LIBRARY"}