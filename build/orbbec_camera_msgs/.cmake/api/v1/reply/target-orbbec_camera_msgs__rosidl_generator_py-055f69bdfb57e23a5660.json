{"artifacts": [{"path": "lib<PERSON><PERSON><PERSON>_camera_msgs__rosidl_generator_py.so"}], "backtrace": 5, "backtraceGraph": {"commands": ["add_library", "include", "ament_execute_extensions", "rosidl_generate_interfaces", "install", "target_link_libraries", "set_target_properties", "find_package", "add_dependencies", "add_definitions", "target_include_directories"], "files": ["/opt/ros/jazzy/share/rosidl_generator_py/cmake/rosidl_generator_py_generate_interfaces.cmake", "/opt/ros/jazzy/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake", "/opt/ros/jazzy/share/rosidl_cmake/cmake/rosidl_generate_interfaces.cmake", "CMakeLists.txt", "/opt/ros/jazzy/share/sensor_msgs/cmake/export_sensor_msgs__rosidl_typesupport_fastrtps_cExport.cmake", "/opt/ros/jazzy/share/sensor_msgs/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/jazzy/share/sensor_msgs/cmake/sensor_msgsConfig.cmake", "/opt/ros/jazzy/share/sensor_msgs/cmake/export_sensor_msgs__rosidl_typesupport_fastrtps_cppExport.cmake", "/opt/ros/jazzy/share/rmw/cmake/rmwExport.cmake", "/opt/ros/jazzy/share/rmw/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/jazzy/share/rmw/cmake/rmwConfig.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_fastrtps_cpp/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_fastrtps_cpp/cmake/rosidl_typesupport_fastrtps_cppConfig.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_cpp/cmake/rosidl_typesupport_cpp-extras.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_cpp/cmake/rosidl_typesupport_cppConfig.cmake", "/opt/ros/jazzy/share/rosidl_core_generators/cmake/rosidl_core_generators-extras.cmake", "/opt/ros/jazzy/share/rosidl_core_generators/cmake/rosidl_core_generatorsConfig.cmake", "/opt/ros/jazzy/share/rosidl_default_generators/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/jazzy/share/rosidl_default_generators/cmake/rosidl_default_generatorsConfig.cmake", "/opt/ros/jazzy/share/sensor_msgs/cmake/sensor_msgs__rosidl_typesupport_introspection_cppExport.cmake", "/opt/ros/jazzy/share/sensor_msgs/cmake/sensor_msgs__rosidl_typesupport_introspection_cExport.cmake", "/opt/ros/jazzy/share/rosidl_generator_c/cmake/rosidl_generator_c_generate_interfaces.cmake", "/opt/ros/jazzy/share/rcutils/cmake/rcutilsExport.cmake", "/opt/ros/jazzy/share/rcutils/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/jazzy/share/rcutils/cmake/rcutilsConfig.cmake", "/opt/ros/jazzy/share/rosidl_runtime_c/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/jazzy/share/rosidl_runtime_c/cmake/rosidl_runtime_cConfig.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_fastrtps_c/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_fastrtps_c/cmake/rosidl_typesupport_fastrtps_cConfig.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_c/cmake/rosidl_typesupport_c-extras.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_c/cmake/rosidl_typesupport_cConfig.cmake", "/opt/ros/jazzy/share/ament_cmake_ros/cmake/ament_cmake_ros-extras.cmake", "/opt/ros/jazzy/share/ament_cmake_ros/cmake/ament_cmake_rosConfig.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_fastrtps_c/cmake/rosidl_typesupport_fastrtps_c_generate_interfaces.cmake"], "nodes": [{"file": 3}, {"command": 3, "file": 3, "line": 17, "parent": 0}, {"command": 2, "file": 2, "line": 280, "parent": 1}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 0, "parent": 3}, {"command": 0, "file": 0, "line": 140, "parent": 4}, {"command": 4, "file": 0, "line": 240, "parent": 4}, {"command": 5, "file": 0, "line": 166, "parent": 4}, {"command": 5, "file": 0, "line": 234, "parent": 4}, {"command": 5, "file": 0, "line": 234, "parent": 4}, {"command": 5, "file": 0, "line": 234, "parent": 4}, {"command": 5, "file": 0, "line": 234, "parent": 4}, {"command": 5, "file": 0, "line": 141, "parent": 4}, {"command": 5, "file": 0, "line": 234, "parent": 4}, {"command": 7, "file": 3, "line": 11, "parent": 0}, {"file": 6, "parent": 14}, {"command": 1, "file": 6, "line": 41, "parent": 15}, {"file": 5, "parent": 16}, {"command": 1, "file": 5, "line": 9, "parent": 17}, {"file": 4, "parent": 18}, {"command": 6, "file": 4, "line": 61, "parent": 19}, {"command": 1, "file": 5, "line": 9, "parent": 17}, {"file": 7, "parent": 21}, {"command": 6, "file": 7, "line": 61, "parent": 22}, {"command": 7, "file": 3, "line": 10, "parent": 0}, {"file": 18, "parent": 24}, {"command": 1, "file": 18, "line": 41, "parent": 25}, {"file": 17, "parent": 26}, {"command": 7, "file": 17, "line": 21, "parent": 27}, {"file": 16, "parent": 28}, {"command": 1, "file": 16, "line": 41, "parent": 29}, {"file": 15, "parent": 30}, {"command": 7, "file": 15, "line": 21, "parent": 31}, {"file": 14, "parent": 32}, {"command": 1, "file": 14, "line": 41, "parent": 33}, {"file": 13, "parent": 34}, {"command": 7, "file": 13, "line": 13, "parent": 35}, {"file": 12, "parent": 36}, {"command": 1, "file": 12, "line": 41, "parent": 37}, {"file": 11, "parent": 38}, {"command": 7, "file": 11, "line": 21, "parent": 39}, {"file": 10, "parent": 40}, {"command": 1, "file": 10, "line": 41, "parent": 41}, {"file": 9, "parent": 42}, {"command": 1, "file": 9, "line": 9, "parent": 43}, {"file": 8, "parent": 44}, {"command": 6, "file": 8, "line": 61, "parent": 45}, {"command": 1, "file": 5, "line": 9, "parent": 17}, {"file": 19, "parent": 47}, {"command": 6, "file": 19, "line": 61, "parent": 48}, {"command": 1, "file": 5, "line": 9, "parent": 17}, {"file": 20, "parent": 50}, {"command": 6, "file": 20, "line": 61, "parent": 51}, {"command": 5, "file": 0, "line": 149, "parent": 4}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 21, "parent": 54}, {"command": 5, "file": 21, "line": 170, "parent": 55}, {"command": 7, "file": 15, "line": 21, "parent": 31}, {"file": 30, "parent": 57}, {"command": 1, "file": 30, "line": 41, "parent": 58}, {"file": 29, "parent": 59}, {"command": 7, "file": 29, "line": 13, "parent": 60}, {"file": 28, "parent": 61}, {"command": 1, "file": 28, "line": 41, "parent": 62}, {"file": 27, "parent": 63}, {"command": 7, "file": 27, "line": 21, "parent": 64}, {"file": 26, "parent": 65}, {"command": 1, "file": 26, "line": 41, "parent": 66}, {"file": 25, "parent": 67}, {"command": 7, "file": 25, "line": 21, "parent": 68}, {"file": 24, "parent": 69}, {"command": 1, "file": 24, "line": 41, "parent": 70}, {"file": 23, "parent": 71}, {"command": 1, "file": 23, "line": 9, "parent": 72}, {"file": 22, "parent": 73}, {"command": 6, "file": 22, "line": 61, "parent": 74}, {"command": 8, "file": 0, "line": 143, "parent": 4}, {"command": 6, "file": 0, "line": 237, "parent": 4}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 33, "parent": 78}, {"command": 7, "file": 33, "line": 21, "parent": 79}, {"file": 32, "parent": 80}, {"command": 1, "file": 32, "line": 41, "parent": 81}, {"file": 31, "parent": 82}, {"command": 9, "file": 31, "line": 25, "parent": 83}, {"command": 10, "file": 0, "line": 154, "parent": 4}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-fPIC"}, {"backtrace": 77, "fragment": "-Wall"}, {"backtrace": 77, "fragment": "-Wextra"}], "defines": [{"backtrace": 8, "define": "FASTCDR_DYN_LINK"}, {"backtrace": 84, "define": "ROS_PACKAGE_NAME=\"orbbec_camera_msgs\""}, {"define": "orbbec_camera_msgs__rosidl_generator_py_EXPORTS"}], "includes": [{"backtrace": 85, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_c"}, {"backtrace": 85, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_py"}, {"backtrace": 12, "isSystem": true, "path": "/opt/ros/jazzy/include/sensor_msgs"}, {"backtrace": 12, "isSystem": true, "path": "/opt/ros/jazzy/include/builtin_interfaces"}, {"backtrace": 12, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_runtime_c"}, {"backtrace": 12, "isSystem": true, "path": "/opt/ros/jazzy/include/rcutils"}, {"backtrace": 12, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_typesupport_interface"}, {"backtrace": 12, "isSystem": true, "path": "/opt/ros/jazzy/include/geometry_msgs"}, {"backtrace": 12, "isSystem": true, "path": "/opt/ros/jazzy/include/std_msgs"}, {"backtrace": 12, "isSystem": true, "path": "/opt/ros/jazzy/include/service_msgs"}, {"backtrace": 53, "isSystem": true, "path": "/usr/lib/python3/dist-packages/numpy/core/include"}, {"backtrace": 53, "isSystem": true, "path": "/usr/include/python3.12"}, {"backtrace": 8, "isSystem": true, "path": "/opt/ros/jazzy/include/fastcdr"}, {"backtrace": 8, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_runtime_cpp"}, {"backtrace": 8, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_typesupport_fastrtps_cpp"}, {"backtrace": 8, "isSystem": true, "path": "/opt/ros/jazzy/include/rmw"}, {"backtrace": 8, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_dynamic_typesupport"}, {"backtrace": 8, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_typesupport_fastrtps_c"}, {"backtrace": 8, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_typesupport_introspection_c"}, {"backtrace": 8, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_typesupport_introspection_cpp"}], "language": "C", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]}], "dependencies": [{"backtrace": 12, "id": "orbbec_camera_msgs__rosidl_generator_c::@6890427a1f51a3e7e1df"}, {"backtrace": 76, "id": "orbbec_camera_msgs__rosidl_typesupport_c::@6890427a1f51a3e7e1df"}, {"backtrace": 76, "id": "orbbec_camera_msgs__py::@b99f976d5a7686d60dd0"}], "id": "orbbec_camera_msgs__rosidl_generator_py::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 6, "path": "lib"}, {"backtrace": 6, "path": "lib"}], "prefix": {"path": "/home/<USER>/ws_ros2/install/orbbec_camera_msgs"}}, "link": {"commandFragments": [{"fragment": "", "role": "flags"}, {"fragment": "-Wl,-rpath,/home/<USER>/ws_ros2/build/orbbec_camera_msgs:/opt/ros/jazzy/lib:", "role": "libraries"}, {"backtrace": 7, "fragment": "libor<PERSON><PERSON>_camera_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 8, "fragment": "/opt/ros/jazzy/lib/libsensor_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 8, "fragment": "/opt/ros/jazzy/lib/libsensor_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 8, "fragment": "/opt/ros/jazzy/lib/libsensor_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 8, "fragment": "/opt/ros/jazzy/lib/libsensor_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 8, "fragment": "/opt/ros/jazzy/lib/libsensor_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 9, "fragment": "/opt/ros/jazzy/lib/libgeometry_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 9, "fragment": "/opt/ros/jazzy/lib/libgeometry_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 9, "fragment": "/opt/ros/jazzy/lib/libgeometry_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 9, "fragment": "/opt/ros/jazzy/lib/libgeometry_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 9, "fragment": "/opt/ros/jazzy/lib/libgeometry_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/jazzy/lib/libstd_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/jazzy/lib/libstd_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/jazzy/lib/libstd_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/jazzy/lib/libstd_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/jazzy/lib/libstd_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 12, "fragment": "lib<PERSON><PERSON><PERSON>_camera_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 8, "fragment": "/opt/ros/jazzy/lib/libsensor_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 9, "fragment": "/opt/ros/jazzy/lib/libgeometry_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/jazzy/lib/libstd_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 13, "fragment": "/opt/ros/jazzy/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 20, "fragment": "/opt/ros/jazzy/lib/librosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 13, "fragment": "/opt/ros/jazzy/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 13, "fragment": "/opt/ros/jazzy/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 20, "fragment": "/opt/ros/jazzy/lib/librosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 20, "fragment": "/opt/ros/jazzy/lib/libfastcdr.so.2.2.5", "role": "libraries"}, {"backtrace": 23, "fragment": "/opt/ros/jazzy/lib/librmw.so", "role": "libraries"}, {"backtrace": 46, "fragment": "/opt/ros/jazzy/lib/librosidl_dynamic_typesupport.so", "role": "libraries"}, {"backtrace": 13, "fragment": "/opt/ros/jazzy/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 49, "fragment": "/opt/ros/jazzy/lib/librosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 52, "fragment": "/opt/ros/jazzy/lib/librosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 13, "fragment": "/opt/ros/jazzy/lib/libbuiltin_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 53, "fragment": "/usr/lib/x86_64-linux-gnu/libpython3.12.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 13, "fragment": "/opt/ros/jazzy/lib/libbuiltin_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 8, "fragment": "/opt/ros/jazzy/lib/libsensor_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 8, "fragment": "/opt/ros/jazzy/lib/libsensor_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 9, "fragment": "/opt/ros/jazzy/lib/libgeometry_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 9, "fragment": "/opt/ros/jazzy/lib/libgeometry_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/jazzy/lib/libstd_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/jazzy/lib/libstd_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 13, "fragment": "/opt/ros/jazzy/lib/libbuiltin_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 13, "fragment": "/opt/ros/jazzy/lib/libbuiltin_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 56, "fragment": "/opt/ros/jazzy/lib/librosidl_runtime_c.so", "role": "libraries"}, {"backtrace": 56, "fragment": "/opt/ros/jazzy/lib/librcutils.so", "role": "libraries"}, {"backtrace": 75, "fragment": "-ldl", "role": "libraries"}, {"fragment": "-Wl,-rpath-link,/opt/ros/jazzy/lib", "role": "libraries"}], "language": "C"}, "name": "orbbec_camera_msgs__rosidl_generator_py", "nameOnDisk": "lib<PERSON><PERSON><PERSON>_camera_msgs__rosidl_generator_py.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]}], "sources": [{"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_py/orbbec_camera_msgs/msg/_device_info_s.c", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_py/orbbec_camera_msgs/msg/_extrinsics_s.c", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_py/orbbec_camera_msgs/msg/_metadata_s.c", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_py/orbbec_camera_msgs/msg/_imu_info_s.c", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_py/orbbec_camera_msgs/msg/_rgbd_s.c", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_py/orbbec_camera_msgs/srv/_get_bool_s.c", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_py/orbbec_camera_msgs/srv/_get_device_info_s.c", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_py/orbbec_camera_msgs/srv/_get_camera_info_s.c", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_py/orbbec_camera_msgs/srv/_get_int32_s.c", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_py/orbbec_camera_msgs/srv/_get_string_s.c", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_py/orbbec_camera_msgs/srv/_set_int32_s.c", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_py/orbbec_camera_msgs/srv/_set_string_s.c", "sourceGroupIndex": 0}], "type": "SHARED_LIBRARY"}