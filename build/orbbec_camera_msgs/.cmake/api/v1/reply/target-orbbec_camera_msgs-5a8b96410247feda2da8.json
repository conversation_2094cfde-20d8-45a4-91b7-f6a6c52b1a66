{"backtrace": 2, "backtraceGraph": {"commands": ["add_custom_target", "rosidl_generate_interfaces", "add_dependencies", "include", "ament_execute_extensions"], "files": ["/opt/ros/jazzy/share/rosidl_cmake/cmake/rosidl_generate_interfaces.cmake", "CMakeLists.txt", "/opt/ros/jazzy/share/rosidl_generator_type_description/cmake/rosidl_generator_type_description_generate_interfaces.cmake", "/opt/ros/jazzy/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake", "/opt/ros/jazzy/share/rosidl_generator_c/cmake/rosidl_generator_c_generate_interfaces.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_fastrtps_c/cmake/rosidl_typesupport_fastrtps_c_generate_interfaces.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_introspection_c/cmake/rosidl_typesupport_introspection_c_generate_interfaces.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_c/cmake/rosidl_typesupport_c_generate_interfaces.cmake", "/opt/ros/jazzy/share/rosidl_generator_cpp/cmake/rosidl_generator_cpp_generate_interfaces.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_fastrtps_cpp/cmake/rosidl_typesupport_fastrtps_cpp_generate_interfaces.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_introspection_cpp/cmake/rosidl_typesupport_introspection_cpp_generate_interfaces.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_cpp/cmake/rosidl_typesupport_cpp_generate_interfaces.cmake"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 17, "parent": 0}, {"command": 0, "file": 0, "line": 207, "parent": 1}, {"command": 4, "file": 0, "line": 280, "parent": 1}, {"command": 3, "file": 3, "line": 48, "parent": 3}, {"file": 2, "parent": 4}, {"command": 2, "file": 2, "line": 97, "parent": 5}, {"command": 3, "file": 3, "line": 48, "parent": 3}, {"file": 4, "parent": 7}, {"command": 2, "file": 4, "line": 179, "parent": 8}, {"command": 3, "file": 3, "line": 48, "parent": 3}, {"file": 5, "parent": 10}, {"command": 2, "file": 5, "line": 161, "parent": 11}, {"command": 3, "file": 3, "line": 48, "parent": 3}, {"file": 6, "parent": 13}, {"command": 2, "file": 6, "line": 157, "parent": 14}, {"command": 3, "file": 3, "line": 48, "parent": 3}, {"file": 7, "parent": 16}, {"command": 2, "file": 7, "line": 151, "parent": 17}, {"command": 3, "file": 3, "line": 48, "parent": 3}, {"file": 8, "parent": 19}, {"command": 2, "file": 8, "line": 164, "parent": 20}, {"command": 3, "file": 3, "line": 48, "parent": 3}, {"file": 9, "parent": 22}, {"command": 2, "file": 9, "line": 180, "parent": 23}, {"command": 3, "file": 3, "line": 48, "parent": 3}, {"file": 10, "parent": 25}, {"command": 2, "file": 10, "line": 151, "parent": 26}, {"command": 3, "file": 3, "line": 48, "parent": 3}, {"file": 11, "parent": 28}, {"command": 2, "file": 11, "line": 151, "parent": 29}]}, "dependencies": [{"backtrace": 6, "id": "orbbec_camera_msgs__rosidl_generator_type_description::@6890427a1f51a3e7e1df"}, {"backtrace": 9, "id": "orbbec_camera_msgs__rosidl_generator_c::@6890427a1f51a3e7e1df"}, {"backtrace": 12, "id": "orbbec_camera_msgs__rosidl_typesupport_fastrtps_c::@6890427a1f51a3e7e1df"}, {"backtrace": 15, "id": "orbbec_camera_msgs__rosidl_typesupport_introspection_c::@6890427a1f51a3e7e1df"}, {"backtrace": 18, "id": "orbbec_camera_msgs__rosidl_typesupport_c::@6890427a1f51a3e7e1df"}, {"backtrace": 21, "id": "orbbec_camera_msgs__cpp::@6890427a1f51a3e7e1df"}, {"backtrace": 24, "id": "orbbec_camera_msgs__rosidl_typesupport_fastrtps_cpp::@6890427a1f51a3e7e1df"}, {"backtrace": 27, "id": "orbbec_camera_msgs__rosidl_typesupport_introspection_cpp::@6890427a1f51a3e7e1df"}, {"backtrace": 30, "id": "orbbec_camera_msgs__rosidl_typesupport_cpp::@6890427a1f51a3e7e1df"}], "id": "orbbec_camera_msgs::@6890427a1f51a3e7e1df", "name": "orbbec_camera_msgs", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]}, {"name": "CMake Rules", "sourceIndexes": [13]}], "sources": [{"backtrace": 2, "path": "msg/DeviceInfo.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "msg/Extrinsics.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "msg/Metadata.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "msg/IMUInfo.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "msg/RGBD.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "srv/GetBool.srv", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "srv/GetDeviceInfo.srv", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "srv/GetCameraInfo.srv", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "srv/GetInt32.srv", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "srv/GetString.srv", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "srv/SetInt32.srv", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "srv/SetString.srv", "sourceGroupIndex": 0}, {"backtrace": 2, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/CMakeFiles/orbbec_camera_msgs", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/CMakeFiles/orbbec_camera_msgs.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}