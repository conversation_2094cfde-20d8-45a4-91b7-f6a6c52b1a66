{"artifacts": [{"path": "libor<PERSON><PERSON>_camera_msgs__rosidl_typesupport_fastrtps_cpp.so"}], "backtrace": 5, "backtraceGraph": {"commands": ["add_library", "include", "ament_execute_extensions", "rosidl_generate_interfaces", "install", "target_link_libraries", "set_target_properties", "find_package", "add_dependencies", "add_compile_options", "target_compile_options", "add_definitions", "target_include_directories"], "files": ["/opt/ros/jazzy/share/rosidl_typesupport_fastrtps_cpp/cmake/rosidl_typesupport_fastrtps_cpp_generate_interfaces.cmake", "/opt/ros/jazzy/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake", "/opt/ros/jazzy/share/rosidl_cmake/cmake/rosidl_generate_interfaces.cmake", "CMakeLists.txt", "/opt/ros/jazzy/share/rmw/cmake/rmwExport.cmake", "/opt/ros/jazzy/share/rmw/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/jazzy/share/rmw/cmake/rmwConfig.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_fastrtps_cpp/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_fastrtps_cpp/cmake/rosidl_typesupport_fastrtps_cppConfig.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_cpp/cmake/rosidl_typesupport_cpp-extras.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_cpp/cmake/rosidl_typesupport_cppConfig.cmake", "/opt/ros/jazzy/share/rosidl_core_generators/cmake/rosidl_core_generators-extras.cmake", "/opt/ros/jazzy/share/rosidl_core_generators/cmake/rosidl_core_generatorsConfig.cmake", "/opt/ros/jazzy/share/rosidl_default_generators/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/jazzy/share/rosidl_default_generators/cmake/rosidl_default_generatorsConfig.cmake", "/opt/ros/jazzy/share/sensor_msgs/cmake/export_sensor_msgs__rosidl_typesupport_fastrtps_cppExport.cmake", "/opt/ros/jazzy/share/sensor_msgs/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/jazzy/share/sensor_msgs/cmake/sensor_msgsConfig.cmake", "/opt/ros/jazzy/share/geometry_msgs/cmake/export_geometry_msgs__rosidl_typesupport_fastrtps_cppExport.cmake", "/opt/ros/jazzy/share/geometry_msgs/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/jazzy/share/geometry_msgs/cmake/geometry_msgsConfig.cmake", "/opt/ros/jazzy/share/sensor_msgs/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/jazzy/share/std_msgs/cmake/export_std_msgs__rosidl_typesupport_fastrtps_cppExport.cmake", "/opt/ros/jazzy/share/std_msgs/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/jazzy/share/std_msgs/cmake/std_msgsConfig.cmake", "/opt/ros/jazzy/share/geometry_msgs/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/jazzy/share/service_msgs/cmake/export_service_msgs__rosidl_typesupport_fastrtps_cppExport.cmake", "/opt/ros/jazzy/share/service_msgs/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/jazzy/share/service_msgs/cmake/service_msgsConfig.cmake", "/opt/ros/jazzy/share/builtin_interfaces/cmake/export_builtin_interfaces__rosidl_typesupport_fastrtps_cppExport.cmake", "/opt/ros/jazzy/share/builtin_interfaces/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/jazzy/share/builtin_interfaces/cmake/builtin_interfacesConfig.cmake", "/opt/ros/jazzy/share/rcutils/cmake/rcutilsExport.cmake", "/opt/ros/jazzy/share/rcutils/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/jazzy/share/rcutils/cmake/rcutilsConfig.cmake", "/opt/ros/jazzy/share/rosidl_runtime_c/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/jazzy/share/rosidl_runtime_c/cmake/rosidl_runtime_cConfig.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_fastrtps_c/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_fastrtps_c/cmake/rosidl_typesupport_fastrtps_cConfig.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_c/cmake/rosidl_typesupport_c-extras.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_c/cmake/rosidl_typesupport_cConfig.cmake", "/opt/ros/jazzy/share/rosidl_generator_cpp/cmake/rosidl_generator_cpp_generate_interfaces.cmake", "/opt/ros/jazzy/share/ament_cmake_ros/cmake/ament_cmake_ros-extras.cmake", "/opt/ros/jazzy/share/ament_cmake_ros/cmake/ament_cmake_rosConfig.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_fastrtps_c/cmake/rosidl_typesupport_fastrtps_c_generate_interfaces.cmake"], "nodes": [{"file": 3}, {"command": 3, "file": 3, "line": 17, "parent": 0}, {"command": 2, "file": 2, "line": 280, "parent": 1}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 0, "parent": 3}, {"command": 0, "file": 0, "line": 132, "parent": 4}, {"command": 4, "file": 0, "line": 202, "parent": 4}, {"command": 5, "file": 0, "line": 170, "parent": 4}, {"command": 5, "file": 0, "line": 170, "parent": 4}, {"command": 5, "file": 0, "line": 170, "parent": 4}, {"command": 5, "file": 0, "line": 170, "parent": 4}, {"command": 5, "file": 0, "line": 175, "parent": 4}, {"command": 5, "file": 0, "line": 170, "parent": 4}, {"command": 5, "file": 0, "line": 160, "parent": 4}, {"command": 7, "file": 3, "line": 10, "parent": 0}, {"file": 14, "parent": 14}, {"command": 1, "file": 14, "line": 41, "parent": 15}, {"file": 13, "parent": 16}, {"command": 7, "file": 13, "line": 21, "parent": 17}, {"file": 12, "parent": 18}, {"command": 1, "file": 12, "line": 41, "parent": 19}, {"file": 11, "parent": 20}, {"command": 7, "file": 11, "line": 21, "parent": 21}, {"file": 10, "parent": 22}, {"command": 1, "file": 10, "line": 41, "parent": 23}, {"file": 9, "parent": 24}, {"command": 7, "file": 9, "line": 13, "parent": 25}, {"file": 8, "parent": 26}, {"command": 1, "file": 8, "line": 41, "parent": 27}, {"file": 7, "parent": 28}, {"command": 7, "file": 7, "line": 21, "parent": 29}, {"file": 6, "parent": 30}, {"command": 1, "file": 6, "line": 41, "parent": 31}, {"file": 5, "parent": 32}, {"command": 1, "file": 5, "line": 9, "parent": 33}, {"file": 4, "parent": 34}, {"command": 6, "file": 4, "line": 61, "parent": 35}, {"command": 7, "file": 3, "line": 11, "parent": 0}, {"file": 17, "parent": 37}, {"command": 1, "file": 17, "line": 41, "parent": 38}, {"file": 16, "parent": 39}, {"command": 1, "file": 16, "line": 9, "parent": 40}, {"file": 15, "parent": 41}, {"command": 6, "file": 15, "line": 61, "parent": 42}, {"command": 1, "file": 17, "line": 41, "parent": 38}, {"file": 21, "parent": 44}, {"command": 7, "file": 21, "line": 21, "parent": 45}, {"file": 20, "parent": 46}, {"command": 1, "file": 20, "line": 41, "parent": 47}, {"file": 19, "parent": 48}, {"command": 1, "file": 19, "line": 9, "parent": 49}, {"file": 18, "parent": 50}, {"command": 6, "file": 18, "line": 61, "parent": 51}, {"command": 1, "file": 20, "line": 41, "parent": 47}, {"file": 25, "parent": 53}, {"command": 7, "file": 25, "line": 21, "parent": 54}, {"file": 24, "parent": 55}, {"command": 1, "file": 24, "line": 41, "parent": 56}, {"file": 23, "parent": 57}, {"command": 1, "file": 23, "line": 9, "parent": 58}, {"file": 22, "parent": 59}, {"command": 6, "file": 22, "line": 61, "parent": 60}, {"command": 7, "file": 21, "line": 21, "parent": 45}, {"file": 28, "parent": 62}, {"command": 1, "file": 28, "line": 41, "parent": 63}, {"file": 27, "parent": 64}, {"command": 1, "file": 27, "line": 9, "parent": 65}, {"file": 26, "parent": 66}, {"command": 6, "file": 26, "line": 61, "parent": 67}, {"command": 7, "file": 21, "line": 21, "parent": 45}, {"file": 31, "parent": 69}, {"command": 1, "file": 31, "line": 41, "parent": 70}, {"file": 30, "parent": 71}, {"command": 1, "file": 30, "line": 9, "parent": 72}, {"file": 29, "parent": 73}, {"command": 6, "file": 29, "line": 61, "parent": 74}, {"command": 7, "file": 11, "line": 21, "parent": 21}, {"file": 40, "parent": 76}, {"command": 1, "file": 40, "line": 41, "parent": 77}, {"file": 39, "parent": 78}, {"command": 7, "file": 39, "line": 13, "parent": 79}, {"file": 38, "parent": 80}, {"command": 1, "file": 38, "line": 41, "parent": 81}, {"file": 37, "parent": 82}, {"command": 7, "file": 37, "line": 21, "parent": 83}, {"file": 36, "parent": 84}, {"command": 1, "file": 36, "line": 41, "parent": 85}, {"file": 35, "parent": 86}, {"command": 7, "file": 35, "line": 21, "parent": 87}, {"file": 34, "parent": 88}, {"command": 1, "file": 34, "line": 41, "parent": 89}, {"file": 33, "parent": 90}, {"command": 1, "file": 33, "line": 9, "parent": 91}, {"file": 32, "parent": 92}, {"command": 6, "file": 32, "line": 61, "parent": 93}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 41, "parent": 95}, {"command": 8, "file": 41, "line": 164, "parent": 96}, {"command": 9, "file": 3, "line": 5, "parent": 0}, {"command": 10, "file": 0, "line": 152, "parent": 4}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 44, "parent": 100}, {"command": 7, "file": 44, "line": 21, "parent": 101}, {"file": 43, "parent": 102}, {"command": 1, "file": 43, "line": 41, "parent": 103}, {"file": 42, "parent": 104}, {"command": 11, "file": 42, "line": 25, "parent": 105}, {"command": 12, "file": 0, "line": 154, "parent": 4}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-std=gnu++17 -fPIC"}, {"backtrace": 98, "fragment": "-Wall"}, {"backtrace": 98, "fragment": "-Wextra"}, {"backtrace": 98, "fragment": "-Wpedantic"}, {"backtrace": 99, "fragment": "-Wredundant-decls"}], "defines": [{"backtrace": 13, "define": "FASTCDR_DYN_LINK"}, {"define": "ROSIDL_TYPESUPPORT_FASTRTPS_CPP_BUILDING_DLL_orbbec_camera_msgs"}, {"backtrace": 106, "define": "ROS_PACKAGE_NAME=\"orbbec_camera_msgs\""}], "includes": [{"backtrace": 107, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_fastrtps_cpp"}, {"backtrace": 11, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_c"}, {"backtrace": 11, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_cpp"}, {"backtrace": 13, "isSystem": true, "path": "/opt/ros/jazzy/include/fastcdr"}, {"backtrace": 13, "isSystem": true, "path": "/opt/ros/jazzy/include/rmw"}, {"backtrace": 13, "isSystem": true, "path": "/opt/ros/jazzy/include/rcutils"}, {"backtrace": 13, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_dynamic_typesupport"}, {"backtrace": 13, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_runtime_c"}, {"backtrace": 13, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_typesupport_interface"}, {"backtrace": 13, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_runtime_cpp"}, {"backtrace": 13, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_typesupport_fastrtps_cpp"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/jazzy/include/sensor_msgs"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/jazzy/include/builtin_interfaces"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/jazzy/include/geometry_msgs"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/jazzy/include/std_msgs"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/jazzy/include/service_msgs"}], "language": "CXX", "languageStandard": {"backtraces": [7], "standard": "17"}, "sourceIndexes": [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22]}], "dependencies": [{"backtrace": 11, "id": "orbbec_camera_msgs__rosidl_generator_c::@6890427a1f51a3e7e1df"}, {"backtrace": 97, "id": "orbbec_camera_msgs__cpp::@6890427a1f51a3e7e1df"}], "id": "orbbec_camera_msgs__rosidl_typesupport_fastrtps_cpp::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 6, "path": "lib"}, {"backtrace": 6, "path": "lib"}], "prefix": {"path": "/home/<USER>/ws_ros2/install/orbbec_camera_msgs"}}, "link": {"commandFragments": [{"fragment": "", "role": "flags"}, {"fragment": "-Wl,-r<PERSON>,/opt/ros/jazzy/lib:/home/<USER>/ws_ros2/build/orbbec_camera_msgs:", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libsensor_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 8, "fragment": "/opt/ros/jazzy/lib/libgeometry_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 9, "fragment": "/opt/ros/jazzy/lib/libstd_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 11, "fragment": "lib<PERSON><PERSON><PERSON>_camera_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/jazzy/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 13, "fragment": "/opt/ros/jazzy/lib/librosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 13, "fragment": "/opt/ros/jazzy/lib/libfastcdr.so.2.2.5", "role": "libraries"}, {"backtrace": 13, "fragment": "/opt/ros/jazzy/lib/librmw.so", "role": "libraries"}, {"backtrace": 36, "fragment": "/opt/ros/jazzy/lib/librosidl_dynamic_typesupport.so", "role": "libraries"}, {"backtrace": 43, "fragment": "/opt/ros/jazzy/lib/libsensor_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 52, "fragment": "/opt/ros/jazzy/lib/libgeometry_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 61, "fragment": "/opt/ros/jazzy/lib/libstd_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 68, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 75, "fragment": "/opt/ros/jazzy/lib/libbuiltin_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 13, "fragment": "/opt/ros/jazzy/lib/librosidl_runtime_c.so", "role": "libraries"}, {"backtrace": 36, "fragment": "/opt/ros/jazzy/lib/librcutils.so", "role": "libraries"}, {"backtrace": 94, "fragment": "-ldl", "role": "libraries"}], "language": "CXX"}, "name": "orbbec_camera_msgs__rosidl_typesupport_fastrtps_cpp", "nameOnDisk": "libor<PERSON><PERSON>_camera_msgs__rosidl_typesupport_fastrtps_cpp.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23]}, {"name": "CMake Rules", "sourceIndexes": [24]}], "sources": [{"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_fastrtps_cpp/orbbec_camera_msgs/msg/detail/dds_fastrtps/device_info__type_support.cpp", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_fastrtps_cpp/orbbec_camera_msgs/msg/detail/device_info__rosidl_typesupport_fastrtps_cpp.hpp", "sourceGroupIndex": 1}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_fastrtps_cpp/orbbec_camera_msgs/msg/detail/dds_fastrtps/extrinsics__type_support.cpp", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_fastrtps_cpp/orbbec_camera_msgs/msg/detail/extrinsics__rosidl_typesupport_fastrtps_cpp.hpp", "sourceGroupIndex": 1}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_fastrtps_cpp/orbbec_camera_msgs/msg/detail/dds_fastrtps/metadata__type_support.cpp", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_fastrtps_cpp/orbbec_camera_msgs/msg/detail/metadata__rosidl_typesupport_fastrtps_cpp.hpp", "sourceGroupIndex": 1}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_fastrtps_cpp/orbbec_camera_msgs/msg/detail/dds_fastrtps/imu_info__type_support.cpp", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_fastrtps_cpp/orbbec_camera_msgs/msg/detail/imu_info__rosidl_typesupport_fastrtps_cpp.hpp", "sourceGroupIndex": 1}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_fastrtps_cpp/orbbec_camera_msgs/msg/detail/dds_fastrtps/rgbd__type_support.cpp", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_fastrtps_cpp/orbbec_camera_msgs/msg/detail/rgbd__rosidl_typesupport_fastrtps_cpp.hpp", "sourceGroupIndex": 1}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_fastrtps_cpp/orbbec_camera_msgs/srv/detail/dds_fastrtps/get_bool__type_support.cpp", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_fastrtps_cpp/orbbec_camera_msgs/srv/detail/get_bool__rosidl_typesupport_fastrtps_cpp.hpp", "sourceGroupIndex": 1}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_fastrtps_cpp/orbbec_camera_msgs/srv/detail/dds_fastrtps/get_device_info__type_support.cpp", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_fastrtps_cpp/orbbec_camera_msgs/srv/detail/get_device_info__rosidl_typesupport_fastrtps_cpp.hpp", "sourceGroupIndex": 1}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_fastrtps_cpp/orbbec_camera_msgs/srv/detail/dds_fastrtps/get_camera_info__type_support.cpp", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_fastrtps_cpp/orbbec_camera_msgs/srv/detail/get_camera_info__rosidl_typesupport_fastrtps_cpp.hpp", "sourceGroupIndex": 1}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_fastrtps_cpp/orbbec_camera_msgs/srv/detail/dds_fastrtps/get_int32__type_support.cpp", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_fastrtps_cpp/orbbec_camera_msgs/srv/detail/get_int32__rosidl_typesupport_fastrtps_cpp.hpp", "sourceGroupIndex": 1}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_fastrtps_cpp/orbbec_camera_msgs/srv/detail/dds_fastrtps/get_string__type_support.cpp", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_fastrtps_cpp/orbbec_camera_msgs/srv/detail/get_string__rosidl_typesupport_fastrtps_cpp.hpp", "sourceGroupIndex": 1}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_fastrtps_cpp/orbbec_camera_msgs/srv/detail/dds_fastrtps/set_int32__type_support.cpp", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_fastrtps_cpp/orbbec_camera_msgs/srv/detail/set_int32__rosidl_typesupport_fastrtps_cpp.hpp", "sourceGroupIndex": 1}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_fastrtps_cpp/orbbec_camera_msgs/srv/detail/dds_fastrtps/set_string__type_support.cpp", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_fastrtps_cpp/orbbec_camera_msgs/srv/detail/set_string__rosidl_typesupport_fastrtps_cpp.hpp", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_fastrtps_cpp/orbbec_camera_msgs/msg/detail/dds_fastrtps/device_info__type_support.cpp.rule", "sourceGroupIndex": 2}], "type": "SHARED_LIBRARY"}