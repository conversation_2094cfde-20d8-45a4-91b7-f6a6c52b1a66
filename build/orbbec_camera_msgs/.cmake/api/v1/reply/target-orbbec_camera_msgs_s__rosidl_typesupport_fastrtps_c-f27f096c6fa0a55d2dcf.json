{"artifacts": [{"path": "rosidl_generator_py/orbbec_camera_msgs/orbbec_camera_msgs_s__rosidl_typesupport_fastrtps_c.so"}], "backtrace": 7, "backtraceGraph": {"commands": ["add_library", "__Python3_add_library", "python3_add_library", "include", "ament_execute_extensions", "rosidl_generate_interfaces", "install", "target_link_libraries", "ament_target_dependencies", "set_target_properties", "find_package", "add_dependencies", "add_definitions", "target_include_directories"], "files": ["/usr/share/cmake-3.28/Modules/FindPython/Support.cmake", "/usr/share/cmake-3.28/Modules/FindPython3.cmake", "/opt/ros/jazzy/share/rosidl_generator_py/cmake/rosidl_generator_py_generate_interfaces.cmake", "/opt/ros/jazzy/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake", "/opt/ros/jazzy/share/rosidl_cmake/cmake/rosidl_generate_interfaces.cmake", "CMakeLists.txt", "/opt/ros/jazzy/share/ament_cmake_target_dependencies/cmake/ament_target_dependencies.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_c/cmake/rosidl_typesupport_c_generate_interfaces.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_fastrtps_c/cmake/rosidl_typesupport_fastrtps_c_generate_interfaces.cmake", "/opt/ros/jazzy/share/rosidl_generator_c/cmake/rosidl_generator_c_generate_interfaces.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_fastrtps_cpp/cmake/rosidl_typesupport_fastrtps_cppExport.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_fastrtps_cpp/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_fastrtps_cpp/cmake/rosidl_typesupport_fastrtps_cppConfig.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_cpp/cmake/rosidl_typesupport_cpp-extras.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_cpp/cmake/rosidl_typesupport_cppConfig.cmake", "/opt/ros/jazzy/share/rosidl_core_generators/cmake/rosidl_core_generators-extras.cmake", "/opt/ros/jazzy/share/rosidl_core_generators/cmake/rosidl_core_generatorsConfig.cmake", "/opt/ros/jazzy/share/rosidl_default_generators/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/jazzy/share/rosidl_default_generators/cmake/rosidl_default_generatorsConfig.cmake", "/opt/ros/jazzy/share/rcutils/cmake/rcutilsExport.cmake", "/opt/ros/jazzy/share/rcutils/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/jazzy/share/rcutils/cmake/rcutilsConfig.cmake", "/opt/ros/jazzy/share/rosidl_runtime_c/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/jazzy/share/rosidl_runtime_c/cmake/rosidl_runtime_cConfig.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_fastrtps_c/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_fastrtps_c/cmake/rosidl_typesupport_fastrtps_cConfig.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_c/cmake/rosidl_typesupport_c-extras.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_c/cmake/rosidl_typesupport_cConfig.cmake", "/opt/ros/jazzy/share/ament_cmake_ros/cmake/ament_cmake_ros-extras.cmake", "/opt/ros/jazzy/share/ament_cmake_ros/cmake/ament_cmake_rosConfig.cmake"], "nodes": [{"file": 5}, {"command": 5, "file": 5, "line": 17, "parent": 0}, {"command": 4, "file": 4, "line": 280, "parent": 1}, {"command": 3, "file": 3, "line": 48, "parent": 2}, {"file": 2, "parent": 3}, {"command": 2, "file": 2, "line": 178, "parent": 4}, {"command": 1, "file": 1, "line": 549, "parent": 5}, {"command": 0, "file": 0, "line": 4073, "parent": 6}, {"command": 6, "file": 2, "line": 226, "parent": 4}, {"command": 7, "file": 2, "line": 196, "parent": 4}, {"command": 8, "file": 2, "line": 220, "parent": 4}, {"command": 7, "file": 6, "line": 152, "parent": 10}, {"command": 3, "file": 3, "line": 48, "parent": 2}, {"file": 7, "parent": 12}, {"command": 7, "file": 7, "line": 146, "parent": 13}, {"command": 7, "file": 7, "line": 146, "parent": 13}, {"command": 7, "file": 7, "line": 146, "parent": 13}, {"command": 7, "file": 7, "line": 146, "parent": 13}, {"command": 7, "file": 7, "line": 146, "parent": 13}, {"command": 3, "file": 3, "line": 48, "parent": 2}, {"file": 8, "parent": 19}, {"command": 7, "file": 8, "line": 147, "parent": 20}, {"command": 7, "file": 8, "line": 157, "parent": 20}, {"command": 3, "file": 3, "line": 48, "parent": 2}, {"file": 9, "parent": 23}, {"command": 7, "file": 9, "line": 165, "parent": 24}, {"command": 7, "file": 8, "line": 157, "parent": 20}, {"command": 7, "file": 9, "line": 165, "parent": 24}, {"command": 7, "file": 8, "line": 157, "parent": 20}, {"command": 7, "file": 9, "line": 165, "parent": 24}, {"command": 7, "file": 8, "line": 157, "parent": 20}, {"command": 7, "file": 8, "line": 157, "parent": 20}, {"command": 7, "file": 8, "line": 131, "parent": 20}, {"command": 10, "file": 5, "line": 10, "parent": 0}, {"file": 18, "parent": 33}, {"command": 3, "file": 18, "line": 41, "parent": 34}, {"file": 17, "parent": 35}, {"command": 10, "file": 17, "line": 21, "parent": 36}, {"file": 16, "parent": 37}, {"command": 3, "file": 16, "line": 41, "parent": 38}, {"file": 15, "parent": 39}, {"command": 10, "file": 15, "line": 21, "parent": 40}, {"file": 14, "parent": 41}, {"command": 3, "file": 14, "line": 41, "parent": 42}, {"file": 13, "parent": 43}, {"command": 10, "file": 13, "line": 13, "parent": 44}, {"file": 12, "parent": 45}, {"command": 3, "file": 12, "line": 41, "parent": 46}, {"file": 11, "parent": 47}, {"command": 3, "file": 11, "line": 9, "parent": 48}, {"file": 10, "parent": 49}, {"command": 9, "file": 10, "line": 61, "parent": 50}, {"command": 7, "file": 9, "line": 165, "parent": 24}, {"command": 7, "file": 9, "line": 165, "parent": 24}, {"command": 10, "file": 15, "line": 21, "parent": 40}, {"file": 27, "parent": 54}, {"command": 3, "file": 27, "line": 41, "parent": 55}, {"file": 26, "parent": 56}, {"command": 10, "file": 26, "line": 13, "parent": 57}, {"file": 25, "parent": 58}, {"command": 3, "file": 25, "line": 41, "parent": 59}, {"file": 24, "parent": 60}, {"command": 10, "file": 24, "line": 21, "parent": 61}, {"file": 23, "parent": 62}, {"command": 3, "file": 23, "line": 41, "parent": 63}, {"file": 22, "parent": 64}, {"command": 10, "file": 22, "line": 21, "parent": 65}, {"file": 21, "parent": 66}, {"command": 3, "file": 21, "line": 41, "parent": 67}, {"file": 20, "parent": 68}, {"command": 3, "file": 20, "line": 9, "parent": 69}, {"file": 19, "parent": 70}, {"command": 9, "file": 19, "line": 61, "parent": 71}, {"command": 11, "file": 2, "line": 216, "parent": 4}, {"command": 11, "file": 2, "line": 181, "parent": 4}, {"command": 9, "file": 2, "line": 190, "parent": 4}, {"command": 10, "file": 8, "line": 21, "parent": 20}, {"file": 29, "parent": 76}, {"command": 3, "file": 29, "line": 41, "parent": 77}, {"file": 28, "parent": 78}, {"command": 12, "file": 28, "line": 25, "parent": 79}, {"command": 13, "file": 2, "line": 206, "parent": 4}, {"command": 13, "file": 6, "line": 148, "parent": 10}, {"command": 7, "file": 0, "line": 4082, "parent": 6}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-fPIC"}, {"backtrace": 75, "fragment": "-Wall"}, {"backtrace": 75, "fragment": "-Wextra"}], "defines": [{"backtrace": 9, "define": "FASTCDR_DYN_LINK"}, {"backtrace": 80, "define": "ROS_PACKAGE_NAME=\"orbbec_camera_msgs\""}, {"define": "orbbec_camera_msgs_s__rosidl_typesupport_fastrtps_c_EXPORTS"}], "includes": [{"backtrace": 81, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_c"}, {"backtrace": 81, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_py"}, {"backtrace": 9, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_fastrtps_c"}, {"backtrace": 82, "isSystem": true, "path": "/opt/ros/jazzy/include/rmw"}, {"backtrace": 83, "isSystem": true, "path": "/usr/include/python3.12"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/jazzy/include/fastcdr"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_runtime_c"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/jazzy/include/rcutils"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_typesupport_interface"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_runtime_cpp"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_typesupport_fastrtps_cpp"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_dynamic_typesupport"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_typesupport_fastrtps_c"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/jazzy/include/sensor_msgs"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/jazzy/include/builtin_interfaces"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/jazzy/include/geometry_msgs"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/jazzy/include/std_msgs"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/jazzy/include/service_msgs"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_typesupport_c"}], "language": "C", "sourceIndexes": [0]}], "dependencies": [{"backtrace": 9, "id": "orbbec_camera_msgs__rosidl_generator_c::@6890427a1f51a3e7e1df"}, {"backtrace": 73, "id": "orbbec_camera_msgs__rosidl_typesupport_fastrtps_c::@6890427a1f51a3e7e1df"}, {"backtrace": 74, "id": "orbbec_camera_msgs__rosidl_typesupport_c::@6890427a1f51a3e7e1df"}, {"backtrace": 9, "id": "orbbec_camera_msgs__rosidl_generator_py::@6890427a1f51a3e7e1df"}, {"backtrace": 74, "id": "orbbec_camera_msgs__py::@b99f976d5a7686d60dd0"}], "id": "orbbec_camera_msgs_s__rosidl_typesupport_fastrtps_c::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 8, "path": "lib/python3.12/site-packages/orbbec_camera_msgs"}], "prefix": {"path": "/home/<USER>/ws_ros2/install/orbbec_camera_msgs"}}, "link": {"commandFragments": [{"fragment": "", "role": "flags"}, {"fragment": "-Wl,-rpath,/home/<USER>/ws_ros2/build/orbbec_camera_msgs:/opt/ros/jazzy/lib:", "role": "libraries"}, {"backtrace": 9, "fragment": "lib<PERSON><PERSON><PERSON>_camera_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 9, "fragment": "libor<PERSON><PERSON>_camera_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 9, "fragment": "libor<PERSON><PERSON>_camera_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/jazzy/lib/librmw.so", "role": "libraries"}, {"backtrace": 14, "fragment": "/opt/ros/jazzy/lib/libsensor_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 15, "fragment": "/opt/ros/jazzy/lib/libgeometry_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 16, "fragment": "/opt/ros/jazzy/lib/libstd_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 17, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 18, "fragment": "/opt/ros/jazzy/lib/libbuiltin_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 9, "fragment": "/opt/ros/jazzy/lib/librosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 21, "fragment": "lib<PERSON><PERSON><PERSON>_camera_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 22, "fragment": "/opt/ros/jazzy/lib/libsensor_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 25, "fragment": "/opt/ros/jazzy/lib/libsensor_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 26, "fragment": "/opt/ros/jazzy/lib/libgeometry_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 27, "fragment": "/opt/ros/jazzy/lib/libgeometry_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 28, "fragment": "/opt/ros/jazzy/lib/libstd_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 29, "fragment": "/opt/ros/jazzy/lib/libstd_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 31, "fragment": "/opt/ros/jazzy/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 32, "fragment": "/opt/ros/jazzy/lib/librosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 51, "fragment": "/opt/ros/jazzy/lib/librmw.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/jazzy/lib/librosidl_dynamic_typesupport.so", "role": "libraries"}, {"backtrace": 32, "fragment": "/opt/ros/jazzy/lib/librosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 32, "fragment": "/opt/ros/jazzy/lib/libfastcdr.so.2.2.5", "role": "libraries"}, {"backtrace": 52, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 53, "fragment": "/opt/ros/jazzy/lib/libbuiltin_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 9, "fragment": "/opt/ros/jazzy/lib/librosidl_runtime_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/jazzy/lib/librcutils.so", "role": "libraries"}, {"backtrace": 72, "fragment": "-ldl", "role": "libraries"}, {"fragment": "-Wl,-rpath-link,/opt/ros/jazzy/lib", "role": "libraries"}], "language": "C"}, "name": "orbbec_camera_msgs_s__rosidl_typesupport_fastrtps_c", "nameOnDisk": "orbbec_camera_msgs_s__rosidl_typesupport_fastrtps_c.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 7, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_py/orbbec_camera_msgs/_orbbec_camera_msgs_s.ep.rosidl_typesupport_fastrtps_c.c", "sourceGroupIndex": 0}], "type": "MODULE_LIBRARY"}