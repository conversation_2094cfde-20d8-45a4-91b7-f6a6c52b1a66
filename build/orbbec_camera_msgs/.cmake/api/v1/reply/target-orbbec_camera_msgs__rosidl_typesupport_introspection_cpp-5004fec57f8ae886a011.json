{"artifacts": [{"path": "lib<PERSON><PERSON><PERSON>_camera_msgs__rosidl_typesupport_introspection_cpp.so"}], "backtrace": 5, "backtraceGraph": {"commands": ["add_library", "include", "ament_execute_extensions", "rosidl_generate_interfaces", "install", "target_link_libraries", "set_target_properties", "find_package", "add_dependencies", "add_compile_options", "add_definitions", "target_include_directories"], "files": ["/opt/ros/jazzy/share/rosidl_typesupport_introspection_cpp/cmake/rosidl_typesupport_introspection_cpp_generate_interfaces.cmake", "/opt/ros/jazzy/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake", "/opt/ros/jazzy/share/rosidl_cmake/cmake/rosidl_generate_interfaces.cmake", "CMakeLists.txt", "/opt/ros/jazzy/share/rosidl_typesupport_introspection_cpp/cmake/rosidl_typesupport_introspection_cppExport.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_introspection_cpp/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_introspection_cpp/cmake/rosidl_typesupport_introspection_cppConfig.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_cpp/cmake/rosidl_typesupport_cpp-extras.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_cpp/cmake/rosidl_typesupport_cppConfig.cmake", "/opt/ros/jazzy/share/rosidl_core_generators/cmake/rosidl_core_generators-extras.cmake", "/opt/ros/jazzy/share/rosidl_core_generators/cmake/rosidl_core_generatorsConfig.cmake", "/opt/ros/jazzy/share/rosidl_default_generators/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/jazzy/share/rosidl_default_generators/cmake/rosidl_default_generatorsConfig.cmake", "/opt/ros/jazzy/share/rosidl_generator_c/cmake/rosidl_generator_c_generate_interfaces.cmake", "/opt/ros/jazzy/share/rcutils/cmake/rcutilsExport.cmake", "/opt/ros/jazzy/share/rcutils/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/jazzy/share/rcutils/cmake/rcutilsConfig.cmake", "/opt/ros/jazzy/share/rosidl_runtime_c/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/jazzy/share/rosidl_runtime_c/cmake/rosidl_runtime_cConfig.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_fastrtps_c/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_fastrtps_c/cmake/rosidl_typesupport_fastrtps_cConfig.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_c/cmake/rosidl_typesupport_c-extras.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_c/cmake/rosidl_typesupport_cConfig.cmake", "/opt/ros/jazzy/share/rosidl_generator_cpp/cmake/rosidl_generator_cpp_generate_interfaces.cmake", "/opt/ros/jazzy/share/ament_cmake_ros/cmake/ament_cmake_ros-extras.cmake", "/opt/ros/jazzy/share/ament_cmake_ros/cmake/ament_cmake_rosConfig.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_fastrtps_c/cmake/rosidl_typesupport_fastrtps_c_generate_interfaces.cmake"], "nodes": [{"file": 3}, {"command": 3, "file": 3, "line": 17, "parent": 0}, {"command": 2, "file": 2, "line": 280, "parent": 1}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 0, "parent": 3}, {"command": 0, "file": 0, "line": 109, "parent": 4}, {"command": 4, "file": 0, "line": 163, "parent": 4}, {"command": 5, "file": 0, "line": 133, "parent": 4}, {"command": 5, "file": 0, "line": 145, "parent": 4}, {"command": 5, "file": 0, "line": 145, "parent": 4}, {"command": 5, "file": 0, "line": 145, "parent": 4}, {"command": 5, "file": 0, "line": 145, "parent": 4}, {"command": 5, "file": 0, "line": 145, "parent": 4}, {"command": 5, "file": 0, "line": 138, "parent": 4}, {"command": 7, "file": 3, "line": 10, "parent": 0}, {"file": 12, "parent": 14}, {"command": 1, "file": 12, "line": 41, "parent": 15}, {"file": 11, "parent": 16}, {"command": 7, "file": 11, "line": 21, "parent": 17}, {"file": 10, "parent": 18}, {"command": 1, "file": 10, "line": 41, "parent": 19}, {"file": 9, "parent": 20}, {"command": 7, "file": 9, "line": 21, "parent": 21}, {"file": 8, "parent": 22}, {"command": 1, "file": 8, "line": 41, "parent": 23}, {"file": 7, "parent": 24}, {"command": 7, "file": 7, "line": 13, "parent": 25}, {"file": 6, "parent": 26}, {"command": 1, "file": 6, "line": 41, "parent": 27}, {"file": 5, "parent": 28}, {"command": 1, "file": 5, "line": 9, "parent": 29}, {"file": 4, "parent": 30}, {"command": 6, "file": 4, "line": 61, "parent": 31}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 13, "parent": 33}, {"command": 5, "file": 13, "line": 165, "parent": 34}, {"command": 5, "file": 13, "line": 165, "parent": 34}, {"command": 5, "file": 13, "line": 165, "parent": 34}, {"command": 5, "file": 13, "line": 165, "parent": 34}, {"command": 5, "file": 13, "line": 165, "parent": 34}, {"command": 5, "file": 13, "line": 170, "parent": 34}, {"command": 7, "file": 9, "line": 21, "parent": 21}, {"file": 22, "parent": 41}, {"command": 1, "file": 22, "line": 41, "parent": 42}, {"file": 21, "parent": 43}, {"command": 7, "file": 21, "line": 13, "parent": 44}, {"file": 20, "parent": 45}, {"command": 1, "file": 20, "line": 41, "parent": 46}, {"file": 19, "parent": 47}, {"command": 7, "file": 19, "line": 21, "parent": 48}, {"file": 18, "parent": 49}, {"command": 1, "file": 18, "line": 41, "parent": 50}, {"file": 17, "parent": 51}, {"command": 7, "file": 17, "line": 21, "parent": 52}, {"file": 16, "parent": 53}, {"command": 1, "file": 16, "line": 41, "parent": 54}, {"file": 15, "parent": 55}, {"command": 1, "file": 15, "line": 9, "parent": 56}, {"file": 14, "parent": 57}, {"command": 6, "file": 14, "line": 61, "parent": 58}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 23, "parent": 60}, {"command": 8, "file": 23, "line": 164, "parent": 61}, {"command": 9, "file": 3, "line": 5, "parent": 0}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 26, "parent": 64}, {"command": 7, "file": 26, "line": 21, "parent": 65}, {"file": 25, "parent": 66}, {"command": 1, "file": 25, "line": 41, "parent": 67}, {"file": 24, "parent": 68}, {"command": 10, "file": 24, "line": 25, "parent": 69}, {"command": 11, "file": 0, "line": 127, "parent": 4}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-std=gnu++17 -fPIC"}, {"backtrace": 63, "fragment": "-Wall"}, {"backtrace": 63, "fragment": "-Wextra"}, {"backtrace": 63, "fragment": "-Wpedantic"}], "defines": [{"define": "ROSIDL_TYPESUPPORT_INTROSPECTION_CPP_BUILDING_DLL"}, {"backtrace": 70, "define": "ROS_PACKAGE_NAME=\"orbbec_camera_msgs\""}], "includes": [{"backtrace": 71, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_introspection_cpp"}, {"backtrace": 7, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_c"}, {"backtrace": 7, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_cpp"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/jazzy/include/sensor_msgs"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/jazzy/include/builtin_interfaces"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_runtime_c"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/jazzy/include/rcutils"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_typesupport_interface"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/jazzy/include/geometry_msgs"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/jazzy/include/std_msgs"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/jazzy/include/service_msgs"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_runtime_cpp"}, {"backtrace": 13, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_typesupport_introspection_cpp"}, {"backtrace": 13, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_typesupport_introspection_c"}], "language": "CXX", "languageStandard": {"backtraces": [7], "standard": "17"}, "sourceIndexes": [12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23]}], "dependencies": [{"backtrace": 7, "id": "orbbec_camera_msgs__rosidl_generator_c::@6890427a1f51a3e7e1df"}, {"backtrace": 62, "id": "orbbec_camera_msgs__cpp::@6890427a1f51a3e7e1df"}], "id": "orbbec_camera_msgs__rosidl_typesupport_introspection_cpp::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 6, "path": "lib"}, {"backtrace": 6, "path": "lib"}], "prefix": {"path": "/home/<USER>/ws_ros2/install/orbbec_camera_msgs"}}, "link": {"commandFragments": [{"fragment": "", "role": "flags"}, {"fragment": "-Wl,-rpath,/home/<USER>/ws_ros2/build/orbbec_camera_msgs:/opt/ros/jazzy/lib:", "role": "libraries"}, {"backtrace": 7, "fragment": "lib<PERSON><PERSON><PERSON>_camera_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 8, "fragment": "/opt/ros/jazzy/lib/libsensor_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 9, "fragment": "/opt/ros/jazzy/lib/libgeometry_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/jazzy/lib/libstd_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/jazzy/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 13, "fragment": "/opt/ros/jazzy/lib/librosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 32, "fragment": "/opt/ros/jazzy/lib/librosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 35, "fragment": "/opt/ros/jazzy/lib/libsensor_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 36, "fragment": "/opt/ros/jazzy/lib/libgeometry_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 37, "fragment": "/opt/ros/jazzy/lib/libstd_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 38, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 39, "fragment": "/opt/ros/jazzy/lib/libbuiltin_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 13, "fragment": "/opt/ros/jazzy/lib/librosidl_runtime_c.so", "role": "libraries"}, {"backtrace": 40, "fragment": "/opt/ros/jazzy/lib/librcutils.so", "role": "libraries"}, {"backtrace": 59, "fragment": "-ldl", "role": "libraries"}], "language": "CXX"}, "name": "orbbec_camera_msgs__rosidl_typesupport_introspection_cpp", "nameOnDisk": "lib<PERSON><PERSON><PERSON>_camera_msgs__rosidl_typesupport_introspection_cpp.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "<PERSON><PERSON>", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]}, {"name": "Source Files", "sourceIndexes": [12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23]}, {"name": "CMake Rules", "sourceIndexes": [24]}], "sources": [{"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_introspection_cpp/orbbec_camera_msgs/msg/detail/device_info__rosidl_typesupport_introspection_cpp.hpp", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_introspection_cpp/orbbec_camera_msgs/msg/detail/extrinsics__rosidl_typesupport_introspection_cpp.hpp", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_introspection_cpp/orbbec_camera_msgs/msg/detail/metadata__rosidl_typesupport_introspection_cpp.hpp", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_introspection_cpp/orbbec_camera_msgs/msg/detail/imu_info__rosidl_typesupport_introspection_cpp.hpp", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_introspection_cpp/orbbec_camera_msgs/msg/detail/rgbd__rosidl_typesupport_introspection_cpp.hpp", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_introspection_cpp/orbbec_camera_msgs/srv/detail/get_bool__rosidl_typesupport_introspection_cpp.hpp", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_introspection_cpp/orbbec_camera_msgs/srv/detail/get_device_info__rosidl_typesupport_introspection_cpp.hpp", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_introspection_cpp/orbbec_camera_msgs/srv/detail/get_camera_info__rosidl_typesupport_introspection_cpp.hpp", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_introspection_cpp/orbbec_camera_msgs/srv/detail/get_int32__rosidl_typesupport_introspection_cpp.hpp", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_introspection_cpp/orbbec_camera_msgs/srv/detail/get_string__rosidl_typesupport_introspection_cpp.hpp", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_introspection_cpp/orbbec_camera_msgs/srv/detail/set_int32__rosidl_typesupport_introspection_cpp.hpp", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_introspection_cpp/orbbec_camera_msgs/srv/detail/set_string__rosidl_typesupport_introspection_cpp.hpp", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_introspection_cpp/orbbec_camera_msgs/msg/detail/device_info__type_support.cpp", "sourceGroupIndex": 1}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_introspection_cpp/orbbec_camera_msgs/msg/detail/extrinsics__type_support.cpp", "sourceGroupIndex": 1}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_introspection_cpp/orbbec_camera_msgs/msg/detail/metadata__type_support.cpp", "sourceGroupIndex": 1}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_introspection_cpp/orbbec_camera_msgs/msg/detail/imu_info__type_support.cpp", "sourceGroupIndex": 1}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_introspection_cpp/orbbec_camera_msgs/msg/detail/rgbd__type_support.cpp", "sourceGroupIndex": 1}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_introspection_cpp/orbbec_camera_msgs/srv/detail/get_bool__type_support.cpp", "sourceGroupIndex": 1}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_introspection_cpp/orbbec_camera_msgs/srv/detail/get_device_info__type_support.cpp", "sourceGroupIndex": 1}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_introspection_cpp/orbbec_camera_msgs/srv/detail/get_camera_info__type_support.cpp", "sourceGroupIndex": 1}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_introspection_cpp/orbbec_camera_msgs/srv/detail/get_int32__type_support.cpp", "sourceGroupIndex": 1}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_introspection_cpp/orbbec_camera_msgs/srv/detail/get_string__type_support.cpp", "sourceGroupIndex": 1}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_introspection_cpp/orbbec_camera_msgs/srv/detail/set_int32__type_support.cpp", "sourceGroupIndex": 1}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_introspection_cpp/orbbec_camera_msgs/srv/detail/set_string__type_support.cpp", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_introspection_cpp/orbbec_camera_msgs/msg/detail/device_info__rosidl_typesupport_introspection_cpp.hpp.rule", "sourceGroupIndex": 2}], "type": "SHARED_LIBRARY"}