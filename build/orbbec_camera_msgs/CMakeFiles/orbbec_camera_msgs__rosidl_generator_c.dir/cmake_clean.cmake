file(REMOVE_RECURSE
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/msg/detail/device_info__description.c.o"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/msg/detail/device_info__description.c.o.d"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/msg/detail/device_info__functions.c.o"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/msg/detail/device_info__functions.c.o.d"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/msg/detail/device_info__type_support.c.o"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/msg/detail/device_info__type_support.c.o.d"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/msg/detail/extrinsics__description.c.o"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/msg/detail/extrinsics__description.c.o.d"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/msg/detail/extrinsics__functions.c.o"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/msg/detail/extrinsics__functions.c.o.d"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/msg/detail/extrinsics__type_support.c.o"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/msg/detail/extrinsics__type_support.c.o.d"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/msg/detail/imu_info__description.c.o"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/msg/detail/imu_info__description.c.o.d"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/msg/detail/imu_info__functions.c.o"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/msg/detail/imu_info__functions.c.o.d"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/msg/detail/imu_info__type_support.c.o"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/msg/detail/imu_info__type_support.c.o.d"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/msg/detail/metadata__description.c.o"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/msg/detail/metadata__description.c.o.d"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/msg/detail/metadata__functions.c.o"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/msg/detail/metadata__functions.c.o.d"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/msg/detail/metadata__type_support.c.o"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/msg/detail/metadata__type_support.c.o.d"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/msg/detail/rgbd__description.c.o"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/msg/detail/rgbd__description.c.o.d"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/msg/detail/rgbd__functions.c.o"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/msg/detail/rgbd__functions.c.o.d"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/msg/detail/rgbd__type_support.c.o"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/msg/detail/rgbd__type_support.c.o.d"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_bool__description.c.o"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_bool__description.c.o.d"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_bool__functions.c.o"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_bool__functions.c.o.d"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_bool__type_support.c.o"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_bool__type_support.c.o.d"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_camera_info__description.c.o"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_camera_info__description.c.o.d"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_camera_info__functions.c.o"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_camera_info__functions.c.o.d"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_camera_info__type_support.c.o"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_camera_info__type_support.c.o.d"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_device_info__description.c.o"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_device_info__description.c.o.d"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_device_info__functions.c.o"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_device_info__functions.c.o.d"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_device_info__type_support.c.o"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_device_info__type_support.c.o.d"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_int32__description.c.o"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_int32__description.c.o.d"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_int32__functions.c.o"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_int32__functions.c.o.d"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_int32__type_support.c.o"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_int32__type_support.c.o.d"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_string__description.c.o"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_string__description.c.o.d"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_string__functions.c.o"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_string__functions.c.o.d"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_string__type_support.c.o"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_string__type_support.c.o.d"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/srv/detail/set_int32__description.c.o"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/srv/detail/set_int32__description.c.o.d"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/srv/detail/set_int32__functions.c.o"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/srv/detail/set_int32__functions.c.o.d"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/srv/detail/set_int32__type_support.c.o"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/srv/detail/set_int32__type_support.c.o.d"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/srv/detail/set_string__description.c.o"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/srv/detail/set_string__description.c.o.d"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/srv/detail/set_string__functions.c.o"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/srv/detail/set_string__functions.c.o.d"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/srv/detail/set_string__type_support.c.o"
  "CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/rosidl_generator_c/orbbec_camera_msgs/srv/detail/set_string__type_support.c.o.d"
  "liborbbec_camera_msgs__rosidl_generator_c.pdb"
  "liborbbec_camera_msgs__rosidl_generator_c.so"
  "rosidl_generator_c/orbbec_camera_msgs/msg/detail/device_info__description.c"
  "rosidl_generator_c/orbbec_camera_msgs/msg/detail/device_info__functions.c"
  "rosidl_generator_c/orbbec_camera_msgs/msg/detail/device_info__functions.h"
  "rosidl_generator_c/orbbec_camera_msgs/msg/detail/device_info__struct.h"
  "rosidl_generator_c/orbbec_camera_msgs/msg/detail/device_info__type_support.c"
  "rosidl_generator_c/orbbec_camera_msgs/msg/detail/device_info__type_support.h"
  "rosidl_generator_c/orbbec_camera_msgs/msg/detail/extrinsics__description.c"
  "rosidl_generator_c/orbbec_camera_msgs/msg/detail/extrinsics__functions.c"
  "rosidl_generator_c/orbbec_camera_msgs/msg/detail/extrinsics__functions.h"
  "rosidl_generator_c/orbbec_camera_msgs/msg/detail/extrinsics__struct.h"
  "rosidl_generator_c/orbbec_camera_msgs/msg/detail/extrinsics__type_support.c"
  "rosidl_generator_c/orbbec_camera_msgs/msg/detail/extrinsics__type_support.h"
  "rosidl_generator_c/orbbec_camera_msgs/msg/detail/imu_info__description.c"
  "rosidl_generator_c/orbbec_camera_msgs/msg/detail/imu_info__functions.c"
  "rosidl_generator_c/orbbec_camera_msgs/msg/detail/imu_info__functions.h"
  "rosidl_generator_c/orbbec_camera_msgs/msg/detail/imu_info__struct.h"
  "rosidl_generator_c/orbbec_camera_msgs/msg/detail/imu_info__type_support.c"
  "rosidl_generator_c/orbbec_camera_msgs/msg/detail/imu_info__type_support.h"
  "rosidl_generator_c/orbbec_camera_msgs/msg/detail/metadata__description.c"
  "rosidl_generator_c/orbbec_camera_msgs/msg/detail/metadata__functions.c"
  "rosidl_generator_c/orbbec_camera_msgs/msg/detail/metadata__functions.h"
  "rosidl_generator_c/orbbec_camera_msgs/msg/detail/metadata__struct.h"
  "rosidl_generator_c/orbbec_camera_msgs/msg/detail/metadata__type_support.c"
  "rosidl_generator_c/orbbec_camera_msgs/msg/detail/metadata__type_support.h"
  "rosidl_generator_c/orbbec_camera_msgs/msg/detail/rgbd__description.c"
  "rosidl_generator_c/orbbec_camera_msgs/msg/detail/rgbd__functions.c"
  "rosidl_generator_c/orbbec_camera_msgs/msg/detail/rgbd__functions.h"
  "rosidl_generator_c/orbbec_camera_msgs/msg/detail/rgbd__struct.h"
  "rosidl_generator_c/orbbec_camera_msgs/msg/detail/rgbd__type_support.c"
  "rosidl_generator_c/orbbec_camera_msgs/msg/detail/rgbd__type_support.h"
  "rosidl_generator_c/orbbec_camera_msgs/msg/device_info.h"
  "rosidl_generator_c/orbbec_camera_msgs/msg/extrinsics.h"
  "rosidl_generator_c/orbbec_camera_msgs/msg/imu_info.h"
  "rosidl_generator_c/orbbec_camera_msgs/msg/metadata.h"
  "rosidl_generator_c/orbbec_camera_msgs/msg/rgbd.h"
  "rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_bool__description.c"
  "rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_bool__functions.c"
  "rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_bool__functions.h"
  "rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_bool__struct.h"
  "rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_bool__type_support.c"
  "rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_bool__type_support.h"
  "rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_camera_info__description.c"
  "rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_camera_info__functions.c"
  "rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_camera_info__functions.h"
  "rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_camera_info__struct.h"
  "rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_camera_info__type_support.c"
  "rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_camera_info__type_support.h"
  "rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_device_info__description.c"
  "rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_device_info__functions.c"
  "rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_device_info__functions.h"
  "rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_device_info__struct.h"
  "rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_device_info__type_support.c"
  "rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_device_info__type_support.h"
  "rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_int32__description.c"
  "rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_int32__functions.c"
  "rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_int32__functions.h"
  "rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_int32__struct.h"
  "rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_int32__type_support.c"
  "rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_int32__type_support.h"
  "rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_string__description.c"
  "rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_string__functions.c"
  "rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_string__functions.h"
  "rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_string__struct.h"
  "rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_string__type_support.c"
  "rosidl_generator_c/orbbec_camera_msgs/srv/detail/get_string__type_support.h"
  "rosidl_generator_c/orbbec_camera_msgs/srv/detail/set_int32__description.c"
  "rosidl_generator_c/orbbec_camera_msgs/srv/detail/set_int32__functions.c"
  "rosidl_generator_c/orbbec_camera_msgs/srv/detail/set_int32__functions.h"
  "rosidl_generator_c/orbbec_camera_msgs/srv/detail/set_int32__struct.h"
  "rosidl_generator_c/orbbec_camera_msgs/srv/detail/set_int32__type_support.c"
  "rosidl_generator_c/orbbec_camera_msgs/srv/detail/set_int32__type_support.h"
  "rosidl_generator_c/orbbec_camera_msgs/srv/detail/set_string__description.c"
  "rosidl_generator_c/orbbec_camera_msgs/srv/detail/set_string__functions.c"
  "rosidl_generator_c/orbbec_camera_msgs/srv/detail/set_string__functions.h"
  "rosidl_generator_c/orbbec_camera_msgs/srv/detail/set_string__struct.h"
  "rosidl_generator_c/orbbec_camera_msgs/srv/detail/set_string__type_support.c"
  "rosidl_generator_c/orbbec_camera_msgs/srv/detail/set_string__type_support.h"
  "rosidl_generator_c/orbbec_camera_msgs/srv/get_bool.h"
  "rosidl_generator_c/orbbec_camera_msgs/srv/get_camera_info.h"
  "rosidl_generator_c/orbbec_camera_msgs/srv/get_device_info.h"
  "rosidl_generator_c/orbbec_camera_msgs/srv/get_int32.h"
  "rosidl_generator_c/orbbec_camera_msgs/srv/get_string.h"
  "rosidl_generator_c/orbbec_camera_msgs/srv/set_int32.h"
  "rosidl_generator_c/orbbec_camera_msgs/srv/set_string.h"
)

# Per-language clean rules from dependency scanning.
foreach(lang C)
  include(CMakeFiles/orbbec_camera_msgs__rosidl_generator_c.dir/cmake_clean_${lang}.cmake OPTIONAL)
endforeach()
