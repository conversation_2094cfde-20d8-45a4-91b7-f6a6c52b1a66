
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  )

# Pairs of files generated by the same build rule.
set(CMAKE_MULTIPLE_OUTPUT_PAIRS
  "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_type_description/orbbec_camera_msgs/msg/Extrinsics.json" "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_type_description/orbbec_camera_msgs/msg/DeviceInfo.json"
  "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_type_description/orbbec_camera_msgs/msg/IMUInfo.json" "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_type_description/orbbec_camera_msgs/msg/DeviceInfo.json"
  "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_type_description/orbbec_camera_msgs/msg/Metadata.json" "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_type_description/orbbec_camera_msgs/msg/DeviceInfo.json"
  "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_type_description/orbbec_camera_msgs/msg/RGBD.json" "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_type_description/orbbec_camera_msgs/msg/DeviceInfo.json"
  "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_type_description/orbbec_camera_msgs/srv/GetBool.json" "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_type_description/orbbec_camera_msgs/msg/DeviceInfo.json"
  "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_type_description/orbbec_camera_msgs/srv/GetCameraInfo.json" "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_type_description/orbbec_camera_msgs/msg/DeviceInfo.json"
  "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_type_description/orbbec_camera_msgs/srv/GetDeviceInfo.json" "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_type_description/orbbec_camera_msgs/msg/DeviceInfo.json"
  "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_type_description/orbbec_camera_msgs/srv/GetInt32.json" "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_type_description/orbbec_camera_msgs/msg/DeviceInfo.json"
  "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_type_description/orbbec_camera_msgs/srv/GetString.json" "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_type_description/orbbec_camera_msgs/msg/DeviceInfo.json"
  "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_type_description/orbbec_camera_msgs/srv/SetInt32.json" "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_type_description/orbbec_camera_msgs/msg/DeviceInfo.json"
  "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_type_description/orbbec_camera_msgs/srv/SetString.json" "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_type_description/orbbec_camera_msgs/msg/DeviceInfo.json"
  )


# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
