# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/ws_ros2/src/Sensors_ROS2/OrbbecSDK_ROS2/orbbec_camera_msgs

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/ws_ros2/build/orbbec_camera_msgs

# Utility rule file for orbbec_camera_msgs__rosidl_generator_type_description.

# Include any custom commands dependencies for this target.
include CMakeFiles/orbbec_camera_msgs__rosidl_generator_type_description.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/orbbec_camera_msgs__rosidl_generator_type_description.dir/progress.make

CMakeFiles/orbbec_camera_msgs__rosidl_generator_type_description: rosidl_generator_type_description/orbbec_camera_msgs/msg/DeviceInfo.json
CMakeFiles/orbbec_camera_msgs__rosidl_generator_type_description: rosidl_generator_type_description/orbbec_camera_msgs/msg/Extrinsics.json
CMakeFiles/orbbec_camera_msgs__rosidl_generator_type_description: rosidl_generator_type_description/orbbec_camera_msgs/msg/Metadata.json
CMakeFiles/orbbec_camera_msgs__rosidl_generator_type_description: rosidl_generator_type_description/orbbec_camera_msgs/msg/IMUInfo.json
CMakeFiles/orbbec_camera_msgs__rosidl_generator_type_description: rosidl_generator_type_description/orbbec_camera_msgs/msg/RGBD.json
CMakeFiles/orbbec_camera_msgs__rosidl_generator_type_description: rosidl_generator_type_description/orbbec_camera_msgs/srv/GetBool.json
CMakeFiles/orbbec_camera_msgs__rosidl_generator_type_description: rosidl_generator_type_description/orbbec_camera_msgs/srv/GetDeviceInfo.json
CMakeFiles/orbbec_camera_msgs__rosidl_generator_type_description: rosidl_generator_type_description/orbbec_camera_msgs/srv/GetCameraInfo.json
CMakeFiles/orbbec_camera_msgs__rosidl_generator_type_description: rosidl_generator_type_description/orbbec_camera_msgs/srv/GetInt32.json
CMakeFiles/orbbec_camera_msgs__rosidl_generator_type_description: rosidl_generator_type_description/orbbec_camera_msgs/srv/GetString.json
CMakeFiles/orbbec_camera_msgs__rosidl_generator_type_description: rosidl_generator_type_description/orbbec_camera_msgs/srv/SetInt32.json
CMakeFiles/orbbec_camera_msgs__rosidl_generator_type_description: rosidl_generator_type_description/orbbec_camera_msgs/srv/SetString.json

rosidl_generator_type_description/orbbec_camera_msgs/msg/DeviceInfo.json: /opt/ros/jazzy/lib/rosidl_generator_type_description/rosidl_generator_type_description
rosidl_generator_type_description/orbbec_camera_msgs/msg/DeviceInfo.json: /opt/ros/jazzy/lib/python3.12/site-packages/rosidl_generator_type_description/__init__.py
rosidl_generator_type_description/orbbec_camera_msgs/msg/DeviceInfo.json: rosidl_adapter/orbbec_camera_msgs/msg/DeviceInfo.idl
rosidl_generator_type_description/orbbec_camera_msgs/msg/DeviceInfo.json: rosidl_adapter/orbbec_camera_msgs/msg/Extrinsics.idl
rosidl_generator_type_description/orbbec_camera_msgs/msg/DeviceInfo.json: rosidl_adapter/orbbec_camera_msgs/msg/Metadata.idl
rosidl_generator_type_description/orbbec_camera_msgs/msg/DeviceInfo.json: rosidl_adapter/orbbec_camera_msgs/msg/IMUInfo.idl
rosidl_generator_type_description/orbbec_camera_msgs/msg/DeviceInfo.json: rosidl_adapter/orbbec_camera_msgs/msg/RGBD.idl
rosidl_generator_type_description/orbbec_camera_msgs/msg/DeviceInfo.json: rosidl_adapter/orbbec_camera_msgs/srv/GetBool.idl
rosidl_generator_type_description/orbbec_camera_msgs/msg/DeviceInfo.json: rosidl_adapter/orbbec_camera_msgs/srv/GetDeviceInfo.idl
rosidl_generator_type_description/orbbec_camera_msgs/msg/DeviceInfo.json: rosidl_adapter/orbbec_camera_msgs/srv/GetCameraInfo.idl
rosidl_generator_type_description/orbbec_camera_msgs/msg/DeviceInfo.json: rosidl_adapter/orbbec_camera_msgs/srv/GetInt32.idl
rosidl_generator_type_description/orbbec_camera_msgs/msg/DeviceInfo.json: rosidl_adapter/orbbec_camera_msgs/srv/GetString.idl
rosidl_generator_type_description/orbbec_camera_msgs/msg/DeviceInfo.json: rosidl_adapter/orbbec_camera_msgs/srv/SetInt32.idl
rosidl_generator_type_description/orbbec_camera_msgs/msg/DeviceInfo.json: rosidl_adapter/orbbec_camera_msgs/srv/SetString.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/ws_ros2/build/orbbec_camera_msgs/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Generating type hashes for ROS interfaces"
	/usr/bin/python3 /opt/ros/jazzy/lib/rosidl_generator_type_description/rosidl_generator_type_description --generator-arguments-file /home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_type_description__arguments.json

rosidl_generator_type_description/orbbec_camera_msgs/msg/Extrinsics.json: rosidl_generator_type_description/orbbec_camera_msgs/msg/DeviceInfo.json
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_type_description/orbbec_camera_msgs/msg/Extrinsics.json

rosidl_generator_type_description/orbbec_camera_msgs/msg/Metadata.json: rosidl_generator_type_description/orbbec_camera_msgs/msg/DeviceInfo.json
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_type_description/orbbec_camera_msgs/msg/Metadata.json

rosidl_generator_type_description/orbbec_camera_msgs/msg/IMUInfo.json: rosidl_generator_type_description/orbbec_camera_msgs/msg/DeviceInfo.json
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_type_description/orbbec_camera_msgs/msg/IMUInfo.json

rosidl_generator_type_description/orbbec_camera_msgs/msg/RGBD.json: rosidl_generator_type_description/orbbec_camera_msgs/msg/DeviceInfo.json
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_type_description/orbbec_camera_msgs/msg/RGBD.json

rosidl_generator_type_description/orbbec_camera_msgs/srv/GetBool.json: rosidl_generator_type_description/orbbec_camera_msgs/msg/DeviceInfo.json
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_type_description/orbbec_camera_msgs/srv/GetBool.json

rosidl_generator_type_description/orbbec_camera_msgs/srv/GetDeviceInfo.json: rosidl_generator_type_description/orbbec_camera_msgs/msg/DeviceInfo.json
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_type_description/orbbec_camera_msgs/srv/GetDeviceInfo.json

rosidl_generator_type_description/orbbec_camera_msgs/srv/GetCameraInfo.json: rosidl_generator_type_description/orbbec_camera_msgs/msg/DeviceInfo.json
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_type_description/orbbec_camera_msgs/srv/GetCameraInfo.json

rosidl_generator_type_description/orbbec_camera_msgs/srv/GetInt32.json: rosidl_generator_type_description/orbbec_camera_msgs/msg/DeviceInfo.json
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_type_description/orbbec_camera_msgs/srv/GetInt32.json

rosidl_generator_type_description/orbbec_camera_msgs/srv/GetString.json: rosidl_generator_type_description/orbbec_camera_msgs/msg/DeviceInfo.json
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_type_description/orbbec_camera_msgs/srv/GetString.json

rosidl_generator_type_description/orbbec_camera_msgs/srv/SetInt32.json: rosidl_generator_type_description/orbbec_camera_msgs/msg/DeviceInfo.json
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_type_description/orbbec_camera_msgs/srv/SetInt32.json

rosidl_generator_type_description/orbbec_camera_msgs/srv/SetString.json: rosidl_generator_type_description/orbbec_camera_msgs/msg/DeviceInfo.json
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_type_description/orbbec_camera_msgs/srv/SetString.json

orbbec_camera_msgs__rosidl_generator_type_description: CMakeFiles/orbbec_camera_msgs__rosidl_generator_type_description
orbbec_camera_msgs__rosidl_generator_type_description: rosidl_generator_type_description/orbbec_camera_msgs/msg/DeviceInfo.json
orbbec_camera_msgs__rosidl_generator_type_description: rosidl_generator_type_description/orbbec_camera_msgs/msg/Extrinsics.json
orbbec_camera_msgs__rosidl_generator_type_description: rosidl_generator_type_description/orbbec_camera_msgs/msg/IMUInfo.json
orbbec_camera_msgs__rosidl_generator_type_description: rosidl_generator_type_description/orbbec_camera_msgs/msg/Metadata.json
orbbec_camera_msgs__rosidl_generator_type_description: rosidl_generator_type_description/orbbec_camera_msgs/msg/RGBD.json
orbbec_camera_msgs__rosidl_generator_type_description: rosidl_generator_type_description/orbbec_camera_msgs/srv/GetBool.json
orbbec_camera_msgs__rosidl_generator_type_description: rosidl_generator_type_description/orbbec_camera_msgs/srv/GetCameraInfo.json
orbbec_camera_msgs__rosidl_generator_type_description: rosidl_generator_type_description/orbbec_camera_msgs/srv/GetDeviceInfo.json
orbbec_camera_msgs__rosidl_generator_type_description: rosidl_generator_type_description/orbbec_camera_msgs/srv/GetInt32.json
orbbec_camera_msgs__rosidl_generator_type_description: rosidl_generator_type_description/orbbec_camera_msgs/srv/GetString.json
orbbec_camera_msgs__rosidl_generator_type_description: rosidl_generator_type_description/orbbec_camera_msgs/srv/SetInt32.json
orbbec_camera_msgs__rosidl_generator_type_description: rosidl_generator_type_description/orbbec_camera_msgs/srv/SetString.json
orbbec_camera_msgs__rosidl_generator_type_description: CMakeFiles/orbbec_camera_msgs__rosidl_generator_type_description.dir/build.make
.PHONY : orbbec_camera_msgs__rosidl_generator_type_description

# Rule to build all files generated by this target.
CMakeFiles/orbbec_camera_msgs__rosidl_generator_type_description.dir/build: orbbec_camera_msgs__rosidl_generator_type_description
.PHONY : CMakeFiles/orbbec_camera_msgs__rosidl_generator_type_description.dir/build

CMakeFiles/orbbec_camera_msgs__rosidl_generator_type_description.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/orbbec_camera_msgs__rosidl_generator_type_description.dir/cmake_clean.cmake
.PHONY : CMakeFiles/orbbec_camera_msgs__rosidl_generator_type_description.dir/clean

CMakeFiles/orbbec_camera_msgs__rosidl_generator_type_description.dir/depend:
	cd /home/<USER>/ws_ros2/build/orbbec_camera_msgs && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/ws_ros2/src/Sensors_ROS2/OrbbecSDK_ROS2/orbbec_camera_msgs /home/<USER>/ws_ros2/src/Sensors_ROS2/OrbbecSDK_ROS2/orbbec_camera_msgs /home/<USER>/ws_ros2/build/orbbec_camera_msgs /home/<USER>/ws_ros2/build/orbbec_camera_msgs /home/<USER>/ws_ros2/build/orbbec_camera_msgs/CMakeFiles/orbbec_camera_msgs__rosidl_generator_type_description.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/orbbec_camera_msgs__rosidl_generator_type_description.dir/depend

