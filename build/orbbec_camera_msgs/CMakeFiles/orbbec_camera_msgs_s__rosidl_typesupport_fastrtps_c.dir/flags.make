# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# compile C with /usr/bin/cc
C_DEFINES = -DFASTCDR_DYN_LINK -DROS_PACKAGE_NAME=\"orbbec_camera_msgs\" -Dorbbec_camera_msgs_s__rosidl_typesupport_fastrtps_c_EXPORTS

C_INCLUDES = -I/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_c -I/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_py -I/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_fastrtps_c -isystem /opt/ros/jazzy/include/rmw -isystem /usr/include/python3.12 -isystem /opt/ros/jazzy/include/fastcdr -isystem /opt/ros/jazzy/include/rosidl_runtime_c -isystem /opt/ros/jazzy/include/rcutils -isystem /opt/ros/jazzy/include/rosidl_typesupport_interface -isystem /opt/ros/jazzy/include/rosidl_runtime_cpp -isystem /opt/ros/jazzy/include/rosidl_typesupport_fastrtps_cpp -isystem /opt/ros/jazzy/include/rosidl_dynamic_typesupport -isystem /opt/ros/jazzy/include/rosidl_typesupport_fastrtps_c -isystem /opt/ros/jazzy/include/sensor_msgs -isystem /opt/ros/jazzy/include/builtin_interfaces -isystem /opt/ros/jazzy/include/geometry_msgs -isystem /opt/ros/jazzy/include/std_msgs -isystem /opt/ros/jazzy/include/service_msgs -isystem /opt/ros/jazzy/include/rosidl_typesupport_c

C_FLAGS = -fPIC -Wall -Wextra

