file(REMOVE_RECURSE
  "CMakeFiles/orbbec_camera_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/orbbec_camera_msgs/msg/detail/device_info__type_support.c.o"
  "CMakeFiles/orbbec_camera_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/orbbec_camera_msgs/msg/detail/device_info__type_support.c.o.d"
  "CMakeFiles/orbbec_camera_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/orbbec_camera_msgs/msg/detail/extrinsics__type_support.c.o"
  "CMakeFiles/orbbec_camera_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/orbbec_camera_msgs/msg/detail/extrinsics__type_support.c.o.d"
  "CMakeFiles/orbbec_camera_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/orbbec_camera_msgs/msg/detail/imu_info__type_support.c.o"
  "CMakeFiles/orbbec_camera_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/orbbec_camera_msgs/msg/detail/imu_info__type_support.c.o.d"
  "CMakeFiles/orbbec_camera_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/orbbec_camera_msgs/msg/detail/metadata__type_support.c.o"
  "CMakeFiles/orbbec_camera_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/orbbec_camera_msgs/msg/detail/metadata__type_support.c.o.d"
  "CMakeFiles/orbbec_camera_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/orbbec_camera_msgs/msg/detail/rgbd__type_support.c.o"
  "CMakeFiles/orbbec_camera_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/orbbec_camera_msgs/msg/detail/rgbd__type_support.c.o.d"
  "CMakeFiles/orbbec_camera_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/orbbec_camera_msgs/srv/detail/get_bool__type_support.c.o"
  "CMakeFiles/orbbec_camera_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/orbbec_camera_msgs/srv/detail/get_bool__type_support.c.o.d"
  "CMakeFiles/orbbec_camera_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/orbbec_camera_msgs/srv/detail/get_camera_info__type_support.c.o"
  "CMakeFiles/orbbec_camera_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/orbbec_camera_msgs/srv/detail/get_camera_info__type_support.c.o.d"
  "CMakeFiles/orbbec_camera_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/orbbec_camera_msgs/srv/detail/get_device_info__type_support.c.o"
  "CMakeFiles/orbbec_camera_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/orbbec_camera_msgs/srv/detail/get_device_info__type_support.c.o.d"
  "CMakeFiles/orbbec_camera_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/orbbec_camera_msgs/srv/detail/get_int32__type_support.c.o"
  "CMakeFiles/orbbec_camera_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/orbbec_camera_msgs/srv/detail/get_int32__type_support.c.o.d"
  "CMakeFiles/orbbec_camera_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/orbbec_camera_msgs/srv/detail/get_string__type_support.c.o"
  "CMakeFiles/orbbec_camera_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/orbbec_camera_msgs/srv/detail/get_string__type_support.c.o.d"
  "CMakeFiles/orbbec_camera_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/orbbec_camera_msgs/srv/detail/set_int32__type_support.c.o"
  "CMakeFiles/orbbec_camera_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/orbbec_camera_msgs/srv/detail/set_int32__type_support.c.o.d"
  "CMakeFiles/orbbec_camera_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/orbbec_camera_msgs/srv/detail/set_string__type_support.c.o"
  "CMakeFiles/orbbec_camera_msgs__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/orbbec_camera_msgs/srv/detail/set_string__type_support.c.o.d"
  "liborbbec_camera_msgs__rosidl_typesupport_introspection_c.pdb"
  "liborbbec_camera_msgs__rosidl_typesupport_introspection_c.so"
  "rosidl_typesupport_introspection_c/orbbec_camera_msgs/msg/detail/device_info__rosidl_typesupport_introspection_c.h"
  "rosidl_typesupport_introspection_c/orbbec_camera_msgs/msg/detail/device_info__type_support.c"
  "rosidl_typesupport_introspection_c/orbbec_camera_msgs/msg/detail/extrinsics__rosidl_typesupport_introspection_c.h"
  "rosidl_typesupport_introspection_c/orbbec_camera_msgs/msg/detail/extrinsics__type_support.c"
  "rosidl_typesupport_introspection_c/orbbec_camera_msgs/msg/detail/imu_info__rosidl_typesupport_introspection_c.h"
  "rosidl_typesupport_introspection_c/orbbec_camera_msgs/msg/detail/imu_info__type_support.c"
  "rosidl_typesupport_introspection_c/orbbec_camera_msgs/msg/detail/metadata__rosidl_typesupport_introspection_c.h"
  "rosidl_typesupport_introspection_c/orbbec_camera_msgs/msg/detail/metadata__type_support.c"
  "rosidl_typesupport_introspection_c/orbbec_camera_msgs/msg/detail/rgbd__rosidl_typesupport_introspection_c.h"
  "rosidl_typesupport_introspection_c/orbbec_camera_msgs/msg/detail/rgbd__type_support.c"
  "rosidl_typesupport_introspection_c/orbbec_camera_msgs/srv/detail/get_bool__rosidl_typesupport_introspection_c.h"
  "rosidl_typesupport_introspection_c/orbbec_camera_msgs/srv/detail/get_bool__type_support.c"
  "rosidl_typesupport_introspection_c/orbbec_camera_msgs/srv/detail/get_camera_info__rosidl_typesupport_introspection_c.h"
  "rosidl_typesupport_introspection_c/orbbec_camera_msgs/srv/detail/get_camera_info__type_support.c"
  "rosidl_typesupport_introspection_c/orbbec_camera_msgs/srv/detail/get_device_info__rosidl_typesupport_introspection_c.h"
  "rosidl_typesupport_introspection_c/orbbec_camera_msgs/srv/detail/get_device_info__type_support.c"
  "rosidl_typesupport_introspection_c/orbbec_camera_msgs/srv/detail/get_int32__rosidl_typesupport_introspection_c.h"
  "rosidl_typesupport_introspection_c/orbbec_camera_msgs/srv/detail/get_int32__type_support.c"
  "rosidl_typesupport_introspection_c/orbbec_camera_msgs/srv/detail/get_string__rosidl_typesupport_introspection_c.h"
  "rosidl_typesupport_introspection_c/orbbec_camera_msgs/srv/detail/get_string__type_support.c"
  "rosidl_typesupport_introspection_c/orbbec_camera_msgs/srv/detail/set_int32__rosidl_typesupport_introspection_c.h"
  "rosidl_typesupport_introspection_c/orbbec_camera_msgs/srv/detail/set_int32__type_support.c"
  "rosidl_typesupport_introspection_c/orbbec_camera_msgs/srv/detail/set_string__rosidl_typesupport_introspection_c.h"
  "rosidl_typesupport_introspection_c/orbbec_camera_msgs/srv/detail/set_string__type_support.c"
)

# Per-language clean rules from dependency scanning.
foreach(lang C)
  include(CMakeFiles/orbbec_camera_msgs__rosidl_typesupport_introspection_c.dir/cmake_clean_${lang}.cmake OPTIONAL)
endforeach()
