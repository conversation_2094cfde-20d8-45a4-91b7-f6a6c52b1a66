
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_cpp/orbbec_camera_msgs/msg/device_info__type_support.cpp" "CMakeFiles/orbbec_camera_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/orbbec_camera_msgs/msg/device_info__type_support.cpp.o" "gcc" "CMakeFiles/orbbec_camera_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/orbbec_camera_msgs/msg/device_info__type_support.cpp.o.d"
  "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_cpp/orbbec_camera_msgs/msg/extrinsics__type_support.cpp" "CMakeFiles/orbbec_camera_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/orbbec_camera_msgs/msg/extrinsics__type_support.cpp.o" "gcc" "CMakeFiles/orbbec_camera_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/orbbec_camera_msgs/msg/extrinsics__type_support.cpp.o.d"
  "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_cpp/orbbec_camera_msgs/msg/imu_info__type_support.cpp" "CMakeFiles/orbbec_camera_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/orbbec_camera_msgs/msg/imu_info__type_support.cpp.o" "gcc" "CMakeFiles/orbbec_camera_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/orbbec_camera_msgs/msg/imu_info__type_support.cpp.o.d"
  "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_cpp/orbbec_camera_msgs/msg/metadata__type_support.cpp" "CMakeFiles/orbbec_camera_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/orbbec_camera_msgs/msg/metadata__type_support.cpp.o" "gcc" "CMakeFiles/orbbec_camera_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/orbbec_camera_msgs/msg/metadata__type_support.cpp.o.d"
  "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_cpp/orbbec_camera_msgs/msg/rgbd__type_support.cpp" "CMakeFiles/orbbec_camera_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/orbbec_camera_msgs/msg/rgbd__type_support.cpp.o" "gcc" "CMakeFiles/orbbec_camera_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/orbbec_camera_msgs/msg/rgbd__type_support.cpp.o.d"
  "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_cpp/orbbec_camera_msgs/srv/get_bool__type_support.cpp" "CMakeFiles/orbbec_camera_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/orbbec_camera_msgs/srv/get_bool__type_support.cpp.o" "gcc" "CMakeFiles/orbbec_camera_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/orbbec_camera_msgs/srv/get_bool__type_support.cpp.o.d"
  "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_cpp/orbbec_camera_msgs/srv/get_camera_info__type_support.cpp" "CMakeFiles/orbbec_camera_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/orbbec_camera_msgs/srv/get_camera_info__type_support.cpp.o" "gcc" "CMakeFiles/orbbec_camera_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/orbbec_camera_msgs/srv/get_camera_info__type_support.cpp.o.d"
  "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_cpp/orbbec_camera_msgs/srv/get_device_info__type_support.cpp" "CMakeFiles/orbbec_camera_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/orbbec_camera_msgs/srv/get_device_info__type_support.cpp.o" "gcc" "CMakeFiles/orbbec_camera_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/orbbec_camera_msgs/srv/get_device_info__type_support.cpp.o.d"
  "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_cpp/orbbec_camera_msgs/srv/get_int32__type_support.cpp" "CMakeFiles/orbbec_camera_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/orbbec_camera_msgs/srv/get_int32__type_support.cpp.o" "gcc" "CMakeFiles/orbbec_camera_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/orbbec_camera_msgs/srv/get_int32__type_support.cpp.o.d"
  "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_cpp/orbbec_camera_msgs/srv/get_string__type_support.cpp" "CMakeFiles/orbbec_camera_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/orbbec_camera_msgs/srv/get_string__type_support.cpp.o" "gcc" "CMakeFiles/orbbec_camera_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/orbbec_camera_msgs/srv/get_string__type_support.cpp.o.d"
  "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_cpp/orbbec_camera_msgs/srv/set_int32__type_support.cpp" "CMakeFiles/orbbec_camera_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/orbbec_camera_msgs/srv/set_int32__type_support.cpp.o" "gcc" "CMakeFiles/orbbec_camera_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/orbbec_camera_msgs/srv/set_int32__type_support.cpp.o.d"
  "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_cpp/orbbec_camera_msgs/srv/set_string__type_support.cpp" "CMakeFiles/orbbec_camera_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/orbbec_camera_msgs/srv/set_string__type_support.cpp.o" "gcc" "CMakeFiles/orbbec_camera_msgs__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/orbbec_camera_msgs/srv/set_string__type_support.cpp.o.d"
  )

# Pairs of files generated by the same build rule.
set(CMAKE_MULTIPLE_OUTPUT_PAIRS
  "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_cpp/orbbec_camera_msgs/msg/extrinsics__type_support.cpp" "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_cpp/orbbec_camera_msgs/msg/device_info__type_support.cpp"
  "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_cpp/orbbec_camera_msgs/msg/imu_info__type_support.cpp" "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_cpp/orbbec_camera_msgs/msg/device_info__type_support.cpp"
  "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_cpp/orbbec_camera_msgs/msg/metadata__type_support.cpp" "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_cpp/orbbec_camera_msgs/msg/device_info__type_support.cpp"
  "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_cpp/orbbec_camera_msgs/msg/rgbd__type_support.cpp" "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_cpp/orbbec_camera_msgs/msg/device_info__type_support.cpp"
  "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_cpp/orbbec_camera_msgs/srv/get_bool__type_support.cpp" "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_cpp/orbbec_camera_msgs/msg/device_info__type_support.cpp"
  "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_cpp/orbbec_camera_msgs/srv/get_camera_info__type_support.cpp" "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_cpp/orbbec_camera_msgs/msg/device_info__type_support.cpp"
  "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_cpp/orbbec_camera_msgs/srv/get_device_info__type_support.cpp" "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_cpp/orbbec_camera_msgs/msg/device_info__type_support.cpp"
  "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_cpp/orbbec_camera_msgs/srv/get_int32__type_support.cpp" "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_cpp/orbbec_camera_msgs/msg/device_info__type_support.cpp"
  "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_cpp/orbbec_camera_msgs/srv/get_string__type_support.cpp" "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_cpp/orbbec_camera_msgs/msg/device_info__type_support.cpp"
  "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_cpp/orbbec_camera_msgs/srv/set_int32__type_support.cpp" "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_cpp/orbbec_camera_msgs/msg/device_info__type_support.cpp"
  "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_cpp/orbbec_camera_msgs/srv/set_string__type_support.cpp" "/home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_typesupport_cpp/orbbec_camera_msgs/msg/device_info__type_support.cpp"
  )


# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
