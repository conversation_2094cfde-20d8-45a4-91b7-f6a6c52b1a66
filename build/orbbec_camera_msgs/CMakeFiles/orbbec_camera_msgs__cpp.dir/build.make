# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/ws_ros2/src/Sensors_ROS2/OrbbecSDK_ROS2/orbbec_camera_msgs

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/ws_ros2/build/orbbec_camera_msgs

# Utility rule file for orbbec_camera_msgs__cpp.

# Include any custom commands dependencies for this target.
include CMakeFiles/orbbec_camera_msgs__cpp.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/orbbec_camera_msgs__cpp.dir/progress.make

CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/device_info__builder.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/device_info__struct.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/device_info__traits.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/device_info__type_support.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/extrinsics.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/extrinsics__builder.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/extrinsics__struct.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/extrinsics__traits.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/extrinsics__type_support.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/metadata.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/metadata__builder.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/metadata__struct.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/metadata__traits.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/metadata__type_support.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/imu_info.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/imu_info__builder.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/imu_info__struct.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/imu_info__traits.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/imu_info__type_support.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/rgbd.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/rgbd__builder.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/rgbd__struct.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/rgbd__traits.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/rgbd__type_support.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/get_bool.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_bool__builder.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_bool__struct.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_bool__traits.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_bool__type_support.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/get_device_info.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_device_info__builder.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_device_info__struct.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_device_info__traits.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_device_info__type_support.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/get_camera_info.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_camera_info__builder.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_camera_info__struct.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_camera_info__traits.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_camera_info__type_support.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/get_int32.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_int32__builder.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_int32__struct.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_int32__traits.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_int32__type_support.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/get_string.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_string__builder.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_string__struct.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_string__traits.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_string__type_support.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/set_int32.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/set_int32__builder.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/set_int32__struct.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/set_int32__traits.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/set_int32__type_support.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/set_string.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/set_string__builder.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/set_string__struct.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/set_string__traits.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/set_string__type_support.hpp
CMakeFiles/orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/rosidl_generator_cpp__visibility_control.hpp

rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/lib/rosidl_generator_cpp/rosidl_generator_cpp
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/lib/python3.12/site-packages/rosidl_generator_cpp/__init__.py
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/rosidl_generator_cpp/resource/action__builder.hpp.em
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/rosidl_generator_cpp/resource/action__struct.hpp.em
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/rosidl_generator_cpp/resource/action__traits.hpp.em
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/rosidl_generator_cpp/resource/action__type_support.hpp.em
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/rosidl_generator_cpp/resource/idl.hpp.em
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/rosidl_generator_cpp/resource/idl__builder.hpp.em
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/rosidl_generator_cpp/resource/idl__struct.hpp.em
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/rosidl_generator_cpp/resource/idl__traits.hpp.em
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/rosidl_generator_cpp/resource/idl__type_support.hpp.em
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/rosidl_generator_cpp/resource/msg__builder.hpp.em
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/rosidl_generator_cpp/resource/msg__struct.hpp.em
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/rosidl_generator_cpp/resource/msg__traits.hpp.em
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/rosidl_generator_cpp/resource/msg__type_support.hpp.em
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/rosidl_generator_cpp/resource/srv__builder.hpp.em
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/rosidl_generator_cpp/resource/srv__struct.hpp.em
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/rosidl_generator_cpp/resource/srv__traits.hpp.em
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/rosidl_generator_cpp/resource/srv__type_support.hpp.em
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: rosidl_adapter/orbbec_camera_msgs/msg/DeviceInfo.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: rosidl_adapter/orbbec_camera_msgs/msg/Extrinsics.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: rosidl_adapter/orbbec_camera_msgs/msg/Metadata.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: rosidl_adapter/orbbec_camera_msgs/msg/IMUInfo.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: rosidl_adapter/orbbec_camera_msgs/msg/RGBD.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: rosidl_adapter/orbbec_camera_msgs/srv/GetBool.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: rosidl_adapter/orbbec_camera_msgs/srv/GetDeviceInfo.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: rosidl_adapter/orbbec_camera_msgs/srv/GetCameraInfo.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: rosidl_adapter/orbbec_camera_msgs/srv/GetInt32.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: rosidl_adapter/orbbec_camera_msgs/srv/GetString.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: rosidl_adapter/orbbec_camera_msgs/srv/SetInt32.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: rosidl_adapter/orbbec_camera_msgs/srv/SetString.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/sensor_msgs/msg/BatteryState.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/sensor_msgs/msg/CameraInfo.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/sensor_msgs/msg/ChannelFloat32.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/sensor_msgs/msg/CompressedImage.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/sensor_msgs/msg/FluidPressure.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/sensor_msgs/msg/Illuminance.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/sensor_msgs/msg/Image.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/sensor_msgs/msg/Imu.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/sensor_msgs/msg/JointState.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/sensor_msgs/msg/Joy.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/sensor_msgs/msg/JoyFeedback.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/sensor_msgs/msg/JoyFeedbackArray.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/sensor_msgs/msg/LaserEcho.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/sensor_msgs/msg/LaserScan.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/sensor_msgs/msg/MagneticField.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/sensor_msgs/msg/MultiDOFJointState.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/sensor_msgs/msg/MultiEchoLaserScan.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/sensor_msgs/msg/NavSatFix.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/sensor_msgs/msg/NavSatStatus.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/sensor_msgs/msg/PointCloud.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/sensor_msgs/msg/PointCloud2.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/sensor_msgs/msg/PointField.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/sensor_msgs/msg/Range.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/sensor_msgs/msg/RegionOfInterest.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/sensor_msgs/msg/RelativeHumidity.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/sensor_msgs/msg/Temperature.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/sensor_msgs/msg/TimeReference.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/sensor_msgs/srv/SetCameraInfo.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/builtin_interfaces/msg/Duration.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/builtin_interfaces/msg/Time.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/geometry_msgs/msg/Accel.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/geometry_msgs/msg/AccelStamped.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/geometry_msgs/msg/AccelWithCovariance.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/geometry_msgs/msg/AccelWithCovarianceStamped.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/geometry_msgs/msg/Inertia.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/geometry_msgs/msg/InertiaStamped.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/geometry_msgs/msg/Point.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/geometry_msgs/msg/Point32.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/geometry_msgs/msg/PointStamped.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/geometry_msgs/msg/Polygon.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/geometry_msgs/msg/PolygonInstance.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/geometry_msgs/msg/PolygonInstanceStamped.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/geometry_msgs/msg/PolygonStamped.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/geometry_msgs/msg/Pose.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/geometry_msgs/msg/Pose2D.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/geometry_msgs/msg/PoseArray.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/geometry_msgs/msg/PoseStamped.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/geometry_msgs/msg/PoseWithCovariance.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/geometry_msgs/msg/PoseWithCovarianceStamped.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/geometry_msgs/msg/Quaternion.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/geometry_msgs/msg/QuaternionStamped.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/geometry_msgs/msg/Transform.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/geometry_msgs/msg/TransformStamped.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/geometry_msgs/msg/Twist.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/geometry_msgs/msg/TwistStamped.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/geometry_msgs/msg/TwistWithCovariance.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/geometry_msgs/msg/TwistWithCovarianceStamped.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/geometry_msgs/msg/Vector3.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/geometry_msgs/msg/Vector3Stamped.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/geometry_msgs/msg/VelocityStamped.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/geometry_msgs/msg/Wrench.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/geometry_msgs/msg/WrenchStamped.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/std_msgs/msg/Bool.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/std_msgs/msg/Byte.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/std_msgs/msg/ByteMultiArray.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/std_msgs/msg/Char.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/std_msgs/msg/ColorRGBA.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/std_msgs/msg/Empty.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/std_msgs/msg/Float32.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/std_msgs/msg/Float32MultiArray.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/std_msgs/msg/Float64.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/std_msgs/msg/Float64MultiArray.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/std_msgs/msg/Header.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/std_msgs/msg/Int16.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/std_msgs/msg/Int16MultiArray.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/std_msgs/msg/Int32.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/std_msgs/msg/Int32MultiArray.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/std_msgs/msg/Int64.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/std_msgs/msg/Int64MultiArray.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/std_msgs/msg/Int8.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/std_msgs/msg/Int8MultiArray.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/std_msgs/msg/MultiArrayDimension.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/std_msgs/msg/MultiArrayLayout.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/std_msgs/msg/String.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/std_msgs/msg/UInt16.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/std_msgs/msg/UInt16MultiArray.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/std_msgs/msg/UInt32.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/std_msgs/msg/UInt32MultiArray.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/std_msgs/msg/UInt64.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/std_msgs/msg/UInt64MultiArray.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/std_msgs/msg/UInt8.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/std_msgs/msg/UInt8MultiArray.idl
rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp: /opt/ros/jazzy/share/service_msgs/msg/ServiceEventInfo.idl
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/ws_ros2/build/orbbec_camera_msgs/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Generating C++ code for ROS interfaces"
	/usr/bin/python3 /opt/ros/jazzy/share/rosidl_generator_cpp/cmake/../../../lib/rosidl_generator_cpp/rosidl_generator_cpp --generator-arguments-file /home/<USER>/ws_ros2/build/orbbec_camera_msgs/rosidl_generator_cpp__arguments.json

rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/device_info__builder.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/device_info__builder.hpp

rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/device_info__struct.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/device_info__struct.hpp

rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/device_info__traits.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/device_info__traits.hpp

rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/device_info__type_support.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/device_info__type_support.hpp

rosidl_generator_cpp/orbbec_camera_msgs/msg/extrinsics.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/msg/extrinsics.hpp

rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/extrinsics__builder.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/extrinsics__builder.hpp

rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/extrinsics__struct.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/extrinsics__struct.hpp

rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/extrinsics__traits.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/extrinsics__traits.hpp

rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/extrinsics__type_support.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/extrinsics__type_support.hpp

rosidl_generator_cpp/orbbec_camera_msgs/msg/metadata.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/msg/metadata.hpp

rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/metadata__builder.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/metadata__builder.hpp

rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/metadata__struct.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/metadata__struct.hpp

rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/metadata__traits.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/metadata__traits.hpp

rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/metadata__type_support.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/metadata__type_support.hpp

rosidl_generator_cpp/orbbec_camera_msgs/msg/imu_info.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/msg/imu_info.hpp

rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/imu_info__builder.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/imu_info__builder.hpp

rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/imu_info__struct.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/imu_info__struct.hpp

rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/imu_info__traits.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/imu_info__traits.hpp

rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/imu_info__type_support.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/imu_info__type_support.hpp

rosidl_generator_cpp/orbbec_camera_msgs/msg/rgbd.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/msg/rgbd.hpp

rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/rgbd__builder.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/rgbd__builder.hpp

rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/rgbd__struct.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/rgbd__struct.hpp

rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/rgbd__traits.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/rgbd__traits.hpp

rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/rgbd__type_support.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/rgbd__type_support.hpp

rosidl_generator_cpp/orbbec_camera_msgs/srv/get_bool.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/srv/get_bool.hpp

rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_bool__builder.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_bool__builder.hpp

rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_bool__struct.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_bool__struct.hpp

rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_bool__traits.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_bool__traits.hpp

rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_bool__type_support.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_bool__type_support.hpp

rosidl_generator_cpp/orbbec_camera_msgs/srv/get_device_info.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/srv/get_device_info.hpp

rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_device_info__builder.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_device_info__builder.hpp

rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_device_info__struct.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_device_info__struct.hpp

rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_device_info__traits.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_device_info__traits.hpp

rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_device_info__type_support.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_device_info__type_support.hpp

rosidl_generator_cpp/orbbec_camera_msgs/srv/get_camera_info.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/srv/get_camera_info.hpp

rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_camera_info__builder.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_camera_info__builder.hpp

rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_camera_info__struct.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_camera_info__struct.hpp

rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_camera_info__traits.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_camera_info__traits.hpp

rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_camera_info__type_support.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_camera_info__type_support.hpp

rosidl_generator_cpp/orbbec_camera_msgs/srv/get_int32.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/srv/get_int32.hpp

rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_int32__builder.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_int32__builder.hpp

rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_int32__struct.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_int32__struct.hpp

rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_int32__traits.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_int32__traits.hpp

rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_int32__type_support.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_int32__type_support.hpp

rosidl_generator_cpp/orbbec_camera_msgs/srv/get_string.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/srv/get_string.hpp

rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_string__builder.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_string__builder.hpp

rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_string__struct.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_string__struct.hpp

rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_string__traits.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_string__traits.hpp

rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_string__type_support.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_string__type_support.hpp

rosidl_generator_cpp/orbbec_camera_msgs/srv/set_int32.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/srv/set_int32.hpp

rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/set_int32__builder.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/set_int32__builder.hpp

rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/set_int32__struct.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/set_int32__struct.hpp

rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/set_int32__traits.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/set_int32__traits.hpp

rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/set_int32__type_support.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/set_int32__type_support.hpp

rosidl_generator_cpp/orbbec_camera_msgs/srv/set_string.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/srv/set_string.hpp

rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/set_string__builder.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/set_string__builder.hpp

rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/set_string__struct.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/set_string__struct.hpp

rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/set_string__traits.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/set_string__traits.hpp

rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/set_string__type_support.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/set_string__type_support.hpp

rosidl_generator_cpp/orbbec_camera_msgs/msg/rosidl_generator_cpp__visibility_control.hpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
	@$(CMAKE_COMMAND) -E touch_nocreate rosidl_generator_cpp/orbbec_camera_msgs/msg/rosidl_generator_cpp__visibility_control.hpp

orbbec_camera_msgs__cpp: CMakeFiles/orbbec_camera_msgs__cpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/device_info__builder.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/device_info__struct.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/device_info__traits.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/device_info__type_support.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/extrinsics__builder.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/extrinsics__struct.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/extrinsics__traits.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/extrinsics__type_support.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/imu_info__builder.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/imu_info__struct.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/imu_info__traits.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/imu_info__type_support.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/metadata__builder.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/metadata__struct.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/metadata__traits.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/metadata__type_support.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/rgbd__builder.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/rgbd__struct.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/rgbd__traits.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/rgbd__type_support.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/extrinsics.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/imu_info.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/metadata.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/rgbd.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/msg/rosidl_generator_cpp__visibility_control.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_bool__builder.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_bool__struct.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_bool__traits.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_bool__type_support.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_camera_info__builder.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_camera_info__struct.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_camera_info__traits.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_camera_info__type_support.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_device_info__builder.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_device_info__struct.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_device_info__traits.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_device_info__type_support.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_int32__builder.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_int32__struct.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_int32__traits.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_int32__type_support.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_string__builder.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_string__struct.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_string__traits.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_string__type_support.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/set_int32__builder.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/set_int32__struct.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/set_int32__traits.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/set_int32__type_support.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/set_string__builder.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/set_string__struct.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/set_string__traits.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/set_string__type_support.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/get_bool.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/get_camera_info.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/get_device_info.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/get_int32.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/get_string.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/set_int32.hpp
orbbec_camera_msgs__cpp: rosidl_generator_cpp/orbbec_camera_msgs/srv/set_string.hpp
orbbec_camera_msgs__cpp: CMakeFiles/orbbec_camera_msgs__cpp.dir/build.make
.PHONY : orbbec_camera_msgs__cpp

# Rule to build all files generated by this target.
CMakeFiles/orbbec_camera_msgs__cpp.dir/build: orbbec_camera_msgs__cpp
.PHONY : CMakeFiles/orbbec_camera_msgs__cpp.dir/build

CMakeFiles/orbbec_camera_msgs__cpp.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/orbbec_camera_msgs__cpp.dir/cmake_clean.cmake
.PHONY : CMakeFiles/orbbec_camera_msgs__cpp.dir/clean

CMakeFiles/orbbec_camera_msgs__cpp.dir/depend:
	cd /home/<USER>/ws_ros2/build/orbbec_camera_msgs && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/ws_ros2/src/Sensors_ROS2/OrbbecSDK_ROS2/orbbec_camera_msgs /home/<USER>/ws_ros2/src/Sensors_ROS2/OrbbecSDK_ROS2/orbbec_camera_msgs /home/<USER>/ws_ros2/build/orbbec_camera_msgs /home/<USER>/ws_ros2/build/orbbec_camera_msgs /home/<USER>/ws_ros2/build/orbbec_camera_msgs/CMakeFiles/orbbec_camera_msgs__cpp.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/orbbec_camera_msgs__cpp.dir/depend

