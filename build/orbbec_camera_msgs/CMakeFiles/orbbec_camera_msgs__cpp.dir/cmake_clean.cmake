file(REMOVE_RECURSE
  "CMakeFiles/orbbec_camera_msgs__cpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/device_info__builder.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/device_info__struct.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/device_info__traits.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/device_info__type_support.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/extrinsics__builder.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/extrinsics__struct.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/extrinsics__traits.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/extrinsics__type_support.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/imu_info__builder.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/imu_info__struct.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/imu_info__traits.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/imu_info__type_support.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/metadata__builder.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/metadata__struct.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/metadata__traits.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/metadata__type_support.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/rgbd__builder.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/rgbd__struct.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/rgbd__traits.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/msg/detail/rgbd__type_support.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/msg/device_info.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/msg/extrinsics.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/msg/imu_info.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/msg/metadata.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/msg/rgbd.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/msg/rosidl_generator_cpp__visibility_control.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_bool__builder.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_bool__struct.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_bool__traits.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_bool__type_support.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_camera_info__builder.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_camera_info__struct.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_camera_info__traits.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_camera_info__type_support.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_device_info__builder.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_device_info__struct.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_device_info__traits.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_device_info__type_support.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_int32__builder.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_int32__struct.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_int32__traits.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_int32__type_support.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_string__builder.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_string__struct.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_string__traits.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/get_string__type_support.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/set_int32__builder.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/set_int32__struct.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/set_int32__traits.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/set_int32__type_support.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/set_string__builder.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/set_string__struct.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/set_string__traits.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/srv/detail/set_string__type_support.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/srv/get_bool.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/srv/get_camera_info.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/srv/get_device_info.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/srv/get_int32.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/srv/get_string.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/srv/set_int32.hpp"
  "rosidl_generator_cpp/orbbec_camera_msgs/srv/set_string.hpp"
)

# Per-language clean rules from dependency scanning.
foreach(lang )
  include(CMakeFiles/orbbec_camera_msgs__cpp.dir/cmake_clean_${lang}.cmake OPTIONAL)
endforeach()
