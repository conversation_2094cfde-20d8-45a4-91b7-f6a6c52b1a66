import os
import h5py
import numpy as np
import threading
import rclpy
from rclpy.node import Node
from rclpy.clock import Clock
from sensor_msgs.msg import Image, JointState
from tf2_ros import Buffer, TransformListener
from geometry_msgs.msg import TransformStamped
from std_msgs.msg import Bool, Int32MultiArray
from cv_bridge import CvBridge
import time

class DelayedActionRecorder(Node):
    def __init__(self):
        super().__init__('dataset_recorder')
        
        # 参数配置
        self.declare_parameters(namespace='',
            parameters=[
                ('camera_topics', [ 'cam_high', 'cam_left_wrist', 'cam_right_wrist']),
                ('grippers', ['left_gripper', 'right_gripper']),
                ('joint_names.left', ['Left_Arm_Joint1', 'Left_Arm_Joint2', 'Left_Arm_Joint3', 
                                    'Left_Arm_Joint4', 'Left_Arm_Joint5', 'Left_Arm_Joint6', 'Left_Arm_Joint7']),
                ('joint_names.right', ['Right_Arm_Joint1', 'Right_Arm_Joint2', 'Right_Arm_Joint3',
                                      'Right_Arm_Joint4', 'Right_Arm_Joint5', 'Right_Arm_Joint6', 'Right_Arm_Joint7']),
                ('sampling_rate', 10),
                ('max_data_age', 0.1),
            ])
        
        # 初始化组件
        self.bridge = CvBridge()
        self.tf_buffer = Buffer()
        self.tf_listener = TransformListener(self.tf_buffer, self)
        self.clock = Clock()
        self.lock = threading.Lock()
        
        # 状态变量
        self.is_recording = False
        self.record_buffer = []
        self.hdf_file = None
        self.step_num = 0

        # 数据缓存
        self.current_joints = None
        self.current_left_gripper = [0.0]
        self.current_right_gripper = [0.0]
        self.current_left_gripper_pos = np.zeros(6, dtype=np.int32)
        self.current_left_gripper_force = np.zeros(6, dtype=np.int32)
        self.current_right_gripper_pos = np.zeros(6, dtype=np.int32)
        self.current_right_gripper_force = np.zeros(6, dtype=np.int32)
        self.latest_data = {
            'images': {}

        }

        # 初始化订阅器
        self.init_subscribers()
        
        # 严格定时采样
        self.sampling_timer = self.create_timer(
            1.0/self.get_parameter('sampling_rate').value, 
            self.sampling_callback
        )

    def init_subscribers(self):
        """初始化数据订阅"""
        # 录制命令
        self.record_cmd_sub = self.create_subscription(
            Bool, '/record_command', self.record_cmd_callback, 10)
        
        # 关节状态
        self.joint_sub = self.create_subscription(
            JointState, '/joint_states', self.joint_callback, 10)
        
        # 夹爪数据订阅
        self.create_subscription(
            Int32MultiArray, '/left_gripper_position',
            self.left_pos_callback, 10)
        self.create_subscription(
            Int32MultiArray, '/left_gripper_force',
            self.left_force_callback, 10)
        self.create_subscription(
            Int32MultiArray, '/right_gripper_position',
            self.right_pos_callback, 10)
        self.create_subscription(
            Int32MultiArray, '/right_gripper_force',
            self.right_force_callback, 10)

        # 相机订阅
        for cam in self.get_parameter('camera_topics').value:
            self.create_subscription(
                Image, f'/{cam}/{cam}/color/image_raw',
                lambda msg, c=cam: self.image_callback(msg, c, 'color'),
                10)
            if cam == 'cam_high':
                self.create_subscription(
                    Image, f'/{cam}/{cam}/depth/image_raw',
                    lambda msg, c=cam: self.image_callback(msg, c, 'depth'),
                    10)
            else:
                self.create_subscription(
                    Image, f'/{cam}/{cam}/depth/image_rect_raw',
                    lambda msg, c=cam: self.image_callback(msg, c, 'depth'),
                    10)

    def joint_callback(self, msg):
        """关节状态回调"""
        with self.lock:
            # 解析关节数据
            left_joints = self.get_parameter('joint_names.left').value
            right_joints = self.get_parameter('joint_names.right').value
            
            joint_dict = {name: pos for name, pos in zip(msg.name, msg.position)}
            joint_data = np.array(
                [joint_dict[name] for name in left_joints + right_joints],
                dtype=np.float32
            )
            
            self.current_joints = joint_data


    def left_pos_callback(self, msg):
        with self.lock:
            self.current_left_gripper_pos = np.array(msg.data, dtype=np.int32)

    def left_force_callback(self, msg):
        with self.lock:
            self.current_left_gripper_force = np.array(msg.data, dtype=np.int32)

    def right_pos_callback(self, msg):
        with self.lock:
            self.current_right_gripper_pos = np.array(msg.data, dtype=np.int32)

    def right_force_callback(self, msg):
        with self.lock:
            self.current_right_gripper_force = np.array(msg.data, dtype=np.int32)


    def image_callback(self, msg, camera_name, image_type):
        """图像数据回调"""
        with self.lock:
            if camera_name not in self.latest_data['images']:
                self.latest_data['images'][camera_name] = {}
            
            try:
                if image_type == 'color':
                    img = self.bridge.imgmsg_to_cv2(msg, "bgr8")
                else:
                    img = self.bridge.imgmsg_to_cv2(msg, "32FC1")
                self.latest_data['images'][camera_name][image_type] = img.astype(np.uint16)
            except Exception as e:
                self.get_logger().error(f"Image processing failed: {str(e)}")

    def get_eef_poses(self):
        """获取末端执行器位姿"""
        try:
            now = rclpy.time.Time()
            left_eef = self.tf_buffer.lookup_transform(
                'Body_Link5', 'Left_Arm_Link8', now)
            right_eef = self.tf_buffer.lookup_transform(
                'Body_Link5', 'Right_Arm_Link8', now)
            return np.stack([
                self.transform_to_array(left_eef.transform),
                self.transform_to_array(right_eef.transform)
            ])
        except TransformException as e:
            self.get_logger().warn(f"TF Error: {e}")
            return None

    def transform_to_array(self, transform):
        """转换Transform到numpy数组"""
        return np.array([
            transform.translation.x,
            transform.translation.y,
            transform.translation.z,
            transform.rotation.x,
            transform.rotation.y,
            transform.rotation.z,
            transform.rotation.w
        ], dtype=np.float32)

    def sampling_callback(self):
        """定时采样入口（增加数据有效性检查）"""
        if not self.is_recording:
            return
        
        with self.lock:
            # 检查数据完整性
            required_cams = self.get_parameter('camera_topics').value
            missing_data = False
            
            # 验证所有相机数据是否到位
            for cam in required_cams:
                if cam not in self.latest_data['images']:
                    print("cam,",cam)
                    print("self.latest_data['images'],",self.latest_data['images'])
                    missing_data = True
                    break
                if 'color' not in self.latest_data['images'][cam] :
                    print("'color' self.latest_data['images'][cam],",self.latest_data['images'][cam])
                    missing_data = True
                    break
                if 'depth' not in self.latest_data['images'][cam]:
                    print("'depth' self.latest_data['images'][cam],",self.latest_data['images'][cam])
                    missing_data = True
                    break
            
            if missing_data:
                self.get_logger().warn("跳过不完整的数据帧")
                return
            
            # 获取最新数据
            try:
                data = {
                    'step':self.step_num,
                    'timestamp': self.clock.now().nanoseconds,
                    'joint_states': self.current_joints.copy(),
                    'left_gripper_pos': self.current_left_gripper_pos.copy(),
                    'left_gripper_force': self.current_left_gripper_force.copy(),
                    'right_gripper_pos': self.current_right_gripper_pos.copy(),
                    'right_gripper_force': self.current_right_gripper_force.copy(),
                    'eef_poses': self.get_eef_poses(),
                    'images': {
                        cam: {
                            'color': self.latest_data['images'][cam]['color'].copy(),
                            'depth': self.latest_data['images'][cam]['depth'].copy()
                        } for cam in required_cams
                    }
                }
                
                # 存入缓冲区
                self.record_buffer.append(data)
                
                # 重置当前数据标记
                self.current_joints = None
                self.latest_data['images'] = {cam: {} for cam in required_cams}

                self.step_num = self.step_num +1
                
            except Exception as e:
                self.get_logger().error(f"数据采集失败: {str(e)}")

    def process_recording(self):
        """后处理生成action数据"""
        if len(self.record_buffer) < 2:
            return
        
        # 生成action序列
        actions = []
        for i in range(len(self.record_buffer)):
            if i < len(self.record_buffer)-1:
                # 常规情况：action = 下一帧的observations
                action_data = {
                    'joint_states': self.record_buffer[i+1]['joint_states'],
                    'eef_poses': self.record_buffer[i+1]['eef_poses']
                }
            else:
                # 最后一帧：action = 本帧observations
                action_data = {
                    'joint_states': self.record_buffer[i]['joint_states'],
                    'eef_poses': self.record_buffer[i]['eef_poses']
                }
            actions.append(action_data)
        
        # 写入HDF5
        self.write_to_hdf5(actions)
        self.step_num = 0


    def write_to_hdf5(self, actions):
        """写入结构化数据"""
        try:
            with h5py.File(self.hdf_file, 'w', driver='core', backing_store=True) as f:
                # 创建基础结构
                f.create_dataset('step', 
                    data=np.array([d['step'] for d in self.record_buffer]))
                
                # 写入observations
                obs_group = f.create_group('observations')
                obs_group.create_dataset('joint_states', 
                    data=np.array([d['joint_states'] for d in self.record_buffer]))
                obs_group.create_dataset('eef_poses',
                    data=np.array([d['eef_poses'] for d in self.record_buffer]))

                obs_group.create_dataset('left_gripper_position', 
                    data=np.array([d['left_gripper_pos'] for d in self.record_buffer]))
                obs_group.create_dataset('left_gripper_force',
                    data=np.array([d['left_gripper_force'] for d in self.record_buffer]))
                obs_group.create_dataset('right_gripper_position', 
                    data=np.array([d['right_gripper_pos'] for d in self.record_buffer]))
                obs_group.create_dataset('right_gripper_force',
                    data=np.array([d['right_gripper_force'] for d in self.record_buffer]))
                
                # 写入images
                img_group = obs_group.create_group('images')
                for cam in self.get_parameter('camera_topics').value:
                    cam_group = img_group.create_group(cam)
                    cam_group.create_dataset('color',
                        data=np.array([d['images'][cam]['color'] for d in self.record_buffer]),
                        dtype=np.uint8,
                        #compression="gzip",  # ← 新增压缩
                        #compression_opts=7
                        compression="lzf",  # 无损压缩
                        shuffle=True,       # 提升压缩率
                        chunks=(10, 240, 320, 3))
                    cam_group.create_dataset('depth',
                        data=np.array([d['images'][cam]['depth'] for d in self.record_buffer]),
                        dtype=np.uint16,
                        #compression="gzip",  # ← 新增压缩
                        #compression_opts=7
                        compression="lzf",  # 无损压缩
                        shuffle=True,       # 提升压缩率
                        chunks=(10, 240, 320))
                
                # 写入action
                act_group = f.create_group('action')
                act_group.create_dataset('joint_states',
                    data=np.array([a['joint_states'] for a in actions]))
                act_group.create_dataset('eef_poses',
                    data=np.array([a['eef_poses'] for a in actions]))
                
        except Exception as e:
            self.get_logger().error(f"写入HDF5失败: {str(e)}")


    def record_cmd_callback(self, msg):
        """录制命令处理"""
        if msg.data and not self.is_recording:
            # 开始录制
            self.is_recording = True
            self.record_buffer = []
            self.hdf_file = f"recording_{self.clock.now().nanoseconds}.h5"
            self.get_logger().info("开始录制...")
            
        elif not msg.data and self.is_recording:
            # 结束录制
            self.get_logger().info("录制正在保存...")
            self.is_recording = False
            self.process_recording()
            self.get_logger().info(f"录制完成，保存到 {self.hdf_file}")

def main():
    rclpy.init()
    recorder = DelayedActionRecorder()
    try:
        rclpy.spin(recorder)
    except KeyboardInterrupt:
        recorder.process_recording()
    finally:
        recorder.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
