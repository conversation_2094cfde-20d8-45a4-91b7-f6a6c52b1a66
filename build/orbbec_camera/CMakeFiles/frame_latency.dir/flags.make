# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# compile CXX with /usr/bin/c++
CXX_DEFINES = -DDEFAULT_RMW_IMPLEMENTATION=rmw_fastrtps_cpp -DFASTCDR_DYN_LINK -DROS_JAZZY -Dframe_latency_EXPORTS

CXX_INCLUDES = -I/home/<USER>/ws_ros2/build/orbbec_camera/include -I/home/<USER>/ws_ros2/src/Sensors_ROS2/OrbbecSDK_ROS2/orbbec_camera/include -I/home/<USER>/ws_ros2/src/Sensors_ROS2/OrbbecSDK_ROS2/orbbec_camera/SDK/include -I/home/<USER>/ws_ros2/src/Sensors_ROS2/OrbbecSDK_ROS2/orbbec_camera/tools -isystem /home/<USER>/ws_ros2/install/orbbec_camera_msgs/include/orbbec_camera_msgs -isystem /opt/ros/jazzy/include/ament_index_cpp -isystem /opt/ros/jazzy/include/builtin_interfaces -isystem /opt/ros/jazzy/include/cv_bridge -isystem /opt/ros/jazzy/include/camera_info_manager -isystem /opt/ros/jazzy/include/image_transport -isystem /opt/ros/jazzy/include/rclcpp -isystem /opt/ros/jazzy/include/rclcpp_components -isystem /opt/ros/jazzy/include/sensor_msgs -isystem /opt/ros/jazzy/include/std_msgs -isystem /opt/ros/jazzy/include/std_srvs -isystem /opt/ros/jazzy/include/tf2 -isystem /opt/ros/jazzy/include/tf2_eigen -isystem /opt/ros/jazzy/include/tf2_msgs -isystem /opt/ros/jazzy/include/tf2_ros -isystem /opt/ros/jazzy/include/tf2_sensor_msgs -isystem /opt/ros/jazzy/include -isystem /opt/ros/jazzy/include/diagnostic_msgs -isystem /opt/ros/jazzy/include/statistics_msgs -isystem /opt/ros/jazzy/include/rcl_interfaces -isystem /usr/include/opencv4 -isystem /usr/include/eigen3 -isystem /opt/ros/jazzy/include/rosidl_runtime_c -isystem /opt/ros/jazzy/include/rcutils -isystem /opt/ros/jazzy/include/rosidl_typesupport_interface -isystem /opt/ros/jazzy/include/fastcdr -isystem /opt/ros/jazzy/include/rosidl_runtime_cpp -isystem /opt/ros/jazzy/include/rosidl_typesupport_fastrtps_cpp -isystem /opt/ros/jazzy/include/rmw -isystem /opt/ros/jazzy/include/rosidl_dynamic_typesupport -isystem /opt/ros/jazzy/include/rosidl_typesupport_fastrtps_c -isystem /opt/ros/jazzy/include/rosidl_typesupport_introspection_c -isystem /opt/ros/jazzy/include/rosidl_typesupport_introspection_cpp -isystem /opt/ros/jazzy/include/geometry_msgs -isystem /opt/ros/jazzy/include/service_msgs -isystem /opt/ros/jazzy/include/libstatistics_collector -isystem /opt/ros/jazzy/include/rcl -isystem /opt/ros/jazzy/include/rcl_logging_interface -isystem /opt/ros/jazzy/include/rcl_yaml_param_parser -isystem /opt/ros/jazzy/include/type_description_interfaces -isystem /opt/ros/jazzy/include/rcpputils -isystem /opt/ros/jazzy/include/rosgraph_msgs -isystem /opt/ros/jazzy/include/rosidl_typesupport_cpp -isystem /opt/ros/jazzy/include/rosidl_typesupport_c -isystem /opt/ros/jazzy/include/tracetools -isystem /opt/ros/jazzy/include/rclcpp_lifecycle -isystem /opt/ros/jazzy/include/lifecycle_msgs -isystem /opt/ros/jazzy/include/rcl_lifecycle -isystem /opt/ros/jazzy/include/message_filters -isystem /opt/ros/jazzy/include/class_loader -isystem /opt/ros/jazzy/include/composition_interfaces -isystem /opt/ros/jazzy/include/rclcpp_action -isystem /opt/ros/jazzy/include/action_msgs -isystem /opt/ros/jazzy/include/unique_identifier_msgs -isystem /opt/ros/jazzy/include/rcl_action

CXX_FLAGS =  -fPIC -O3 -O3 -DNDEBUG -std=gnu++17 -fPIC -Wall -Wextra -Werror -Wno-pedantic -Wno-array-bounds

