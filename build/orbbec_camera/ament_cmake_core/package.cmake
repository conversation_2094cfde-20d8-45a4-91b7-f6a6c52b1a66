set(_AMENT_PACKAGE_NAME "orbbec_camera")
set(orbbec_camera_VERSION "1.5.11")
set(orbbec_camera_MAINTAINER "<PERSON> <<EMAIL>>")
set(orbbec_camera_BUILD_DEPENDS "ament_lint_auto" "ament_lint_common" "ament_index_cpp" "image_transport" "image_publisher" "rclcpp_components" "cv_bridge" "backward_ros" "camera_info_manager" "orbbec_camera_msgs" "builtin_interfaces" "rclcpp" "sensor_msgs" "std_msgs" "std_srvs" "statistics_msgs" "tf2" "tf2_ros" "tf2_sensor_msgs" "tf2_msgs" "diagnostic_updater" "diagnostic_msgs" "libgflags-dev" "nlohmann-json-dev" "libgoogle-glog-dev" "libdw-dev")
set(orbbec_camera_BUILDTOOL_DEPENDS "ament_cmake")
set(orbbec_camera_BUILD_EXPORT_DEPENDS "ament_lint_auto" "ament_lint_common" "ament_index_cpp" "image_transport" "image_publisher" "rclcpp_components" "cv_bridge" "backward_ros" "camera_info_manager" "orbbec_camera_msgs" "builtin_interfaces" "rclcpp" "sensor_msgs" "std_msgs" "std_srvs" "statistics_msgs" "tf2" "tf2_ros" "tf2_sensor_msgs" "tf2_msgs" "diagnostic_updater" "diagnostic_msgs" "libgflags-dev" "nlohmann-json-dev" "libgoogle-glog-dev" "libdw-dev")
set(orbbec_camera_BUILDTOOL_EXPORT_DEPENDS )
set(orbbec_camera_EXEC_DEPENDS "ament_lint_auto" "ament_lint_common" "ament_index_cpp" "image_transport" "image_publisher" "rclcpp_components" "cv_bridge" "backward_ros" "camera_info_manager" "orbbec_camera_msgs" "builtin_interfaces" "rclcpp" "sensor_msgs" "std_msgs" "std_srvs" "statistics_msgs" "tf2" "tf2_ros" "tf2_sensor_msgs" "tf2_msgs" "diagnostic_updater" "diagnostic_msgs" "libgflags-dev" "nlohmann-json-dev" "libgoogle-glog-dev" "libdw-dev")
set(orbbec_camera_TEST_DEPENDS )
set(orbbec_camera_GROUP_DEPENDS )
set(orbbec_camera_MEMBER_OF_GROUPS )
set(orbbec_camera_DEPRECATED "")
set(orbbec_camera_EXPORT_TAGS)
list(APPEND orbbec_camera_EXPORT_TAGS "<build_type>ament_cmake</build_type>")
