# generated from ament/cmake/core/templates/nameConfig.cmake.in

# prevent multiple inclusion
if(_orbbec_camera_CONFIG_INCLUDED)
  # ensure to keep the found flag the same
  if(NOT DEFINED orbbec_camera_FOUND)
    # explicitly set it to FALSE, otherwise <PERSON><PERSON><PERSON> will set it to TRUE
    set(orbbec_camera_FOUND FALSE)
  elseif(NOT orbbec_camera_FOUND)
    # use separate condition to avoid uninitialized variable warning
    set(orbbec_camera_FOUND FALSE)
  endif()
  return()
endif()
set(_orbbec_camera_CONFIG_INCLUDED TRUE)

# output package information
if(NOT orbbec_camera_FIND_QUIETLY)
  message(STATUS "Found orbbec_camera: 1.5.11 (${orbbec_camera_DIR})")
endif()

# warn when using a deprecated package
if(NOT "" STREQUAL "")
  set(_msg "Package 'orbbec_camera' is deprecated")
  # append custom deprecation text if available
  if(NOT "" STREQUAL "TRUE")
    set(_msg "${_msg} ()")
  endif()
  # optionally quiet the deprecation message
  if(NOT orbbec_camera_DEPRECATED_QUIET)
    message(DEPRECATION "${_msg}")
  endif()
endif()

# flag package as ament-based to distinguish it after being find_package()-ed
set(orbbec_camera_FOUND_AMENT_PACKAGE TRUE)

# include all config extra files
set(_extras "ament_cmake_export_include_directories-extras.cmake;ament_cmake_export_libraries-extras.cmake;ament_cmake_export_dependencies-extras.cmake")
foreach(_extra ${_extras})
  include("${orbbec_camera_DIR}/${_extra}")
endforeach()
