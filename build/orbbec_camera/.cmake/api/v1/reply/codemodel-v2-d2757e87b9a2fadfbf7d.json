{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-Release-67191379c33ffac9bc8f.json", "minimumCMakeVersion": {"string": "3.15"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]}], "name": "Release", "projects": [{"directoryIndexes": [0], "name": "orbbec_camera", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]}], "targets": [{"directoryIndex": 0, "id": "frame_latency::@6890427a1f51a3e7e1df", "jsonFile": "target-frame_latency-Release-404d3b8b26ca3b8d6dbd.json", "name": "frame_latency", "projectIndex": 0}, {"directoryIndex": 0, "id": "frame_latency_node::@6890427a1f51a3e7e1df", "jsonFile": "target-frame_latency_node-Release-6e6c262335d499d5104b.json", "name": "frame_latency_node", "projectIndex": 0}, {"directoryIndex": 0, "id": "list_camera_profile_mode_node::@6890427a1f51a3e7e1df", "jsonFile": "target-list_camera_profile_mode_node-Release-2156eac08a1946c19e58.json", "name": "list_camera_profile_mode_node", "projectIndex": 0}, {"directoryIndex": 0, "id": "list_depth_work_mode_node::@6890427a1f51a3e7e1df", "jsonFile": "target-list_depth_work_mode_node-Release-ae781d7d916b9a7db8d0.json", "name": "list_depth_work_mode_node", "projectIndex": 0}, {"directoryIndex": 0, "id": "list_devices_node::@6890427a1f51a3e7e1df", "jsonFile": "target-list_devices_node-Release-536baea0b9726db539a4.json", "name": "list_devices_node", "projectIndex": 0}, {"directoryIndex": 0, "id": "orbbec_camera::@6890427a1f51a3e7e1df", "jsonFile": "target-orbbec_camera-Release-a855295f949d81335467.json", "name": "orbbec_camera", "projectIndex": 0}, {"directoryIndex": 0, "id": "orbbec_camera_node::@6890427a1f51a3e7e1df", "jsonFile": "target-orbbec_camera_node-Release-711569e9a8a46588e53d.json", "name": "orbbec_camera_node", "projectIndex": 0}, {"directoryIndex": 0, "id": "orbbec_camera_uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-orbbec_camera_uninstall-Release-b2cb9e768b5df66b675c.json", "name": "orbbec_camera_uninstall", "projectIndex": 0}, {"directoryIndex": 0, "id": "topic_statistics_node::@6890427a1f51a3e7e1df", "jsonFile": "target-topic_statistics_node-Release-a093f127bb93f5212017.json", "name": "topic_statistics_node", "projectIndex": 0}, {"directoryIndex": 0, "id": "uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-uninstall-Release-f867e44164da9bbd1fb4.json", "name": "uninstall", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/ws_ros2/build/orbbec_camera", "source": "/home/<USER>/ws_ros2/src/Sensors_ROS2/OrbbecSDK_ROS2/orbbec_camera"}, "version": {"major": 2, "minor": 6}}