{"artifacts": [{"path": "orbbec_camera_node"}], "backtrace": 2, "backtraceGraph": {"commands": ["add_executable", "rclcpp_components_register_node", "install", "target_link_libraries", "set_target_properties", "include", "find_package", "add_compile_options", "add_compile_definitions"], "files": ["/opt/ros/jazzy/share/rclcpp_components/cmake/rclcpp_components_register_node.cmake", "CMakeLists.txt", "/opt/ros/jazzy/share/class_loader/cmake/class_loaderExport.cmake", "/opt/ros/jazzy/share/class_loader/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/jazzy/share/class_loader/cmake/class_loaderConfig.cmake", "/opt/ros/jazzy/share/pluginlib/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/jazzy/share/pluginlib/cmake/pluginlibConfig.cmake", "/opt/ros/jazzy/share/image_transport/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/jazzy/share/image_transport/cmake/image_transportConfig.cmake", "/opt/ros/jazzy/share/rclcpp/cmake/rclcppExport.cmake", "/opt/ros/jazzy/share/rclcpp/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/jazzy/share/rclcpp/cmake/rclcppConfig.cmake", "/opt/ros/jazzy/share/cv_bridge/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/jazzy/share/cv_bridge/cmake/cv_bridgeConfig.cmake", "/opt/ros/jazzy/share/rcl/cmake/rclExport.cmake", "/opt/ros/jazzy/share/rcl/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/jazzy/share/rcl/cmake/rclConfig.cmake", "/opt/ros/jazzy/share/libstatistics_collector/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/jazzy/share/libstatistics_collector/cmake/libstatistics_collectorConfig.cmake", "/opt/ros/jazzy/share/rclcpp/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/jazzy/share/rcl_interfaces/cmake/export_rcl_interfaces__rosidl_typesupport_fastrtps_cExport.cmake", "/opt/ros/jazzy/share/rcl_interfaces/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/jazzy/share/rcl_interfaces/cmake/rcl_interfacesConfig.cmake", "/opt/ros/jazzy/share/rcl/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/jazzy/share/rcl_interfaces/cmake/rcl_interfaces__rosidl_typesupport_introspection_cExport.cmake", "/opt/ros/jazzy/share/rcl_interfaces/cmake/export_rcl_interfaces__rosidl_typesupport_fastrtps_cppExport.cmake", "/opt/ros/jazzy/share/rcl_interfaces/cmake/rcl_interfaces__rosidl_typesupport_introspection_cppExport.cmake", "/opt/ros/jazzy/share/rcl_interfaces/cmake/rcl_interfaces__rosidl_typesupport_cppExport.cmake", "/opt/ros/jazzy/share/rcl_interfaces/cmake/rcl_interfaces__rosidl_typesupport_cExport.cmake", "/opt/ros/jazzy/share/rcl_interfaces/cmake/export_rcl_interfaces__rosidl_generator_cExport.cmake", "/opt/ros/jazzy/share/builtin_interfaces/cmake/export_builtin_interfaces__rosidl_typesupport_fastrtps_cExport.cmake", "/opt/ros/jazzy/share/builtin_interfaces/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/jazzy/share/builtin_interfaces/cmake/builtin_interfacesConfig.cmake", "/opt/ros/jazzy/share/builtin_interfaces/cmake/builtin_interfaces__rosidl_typesupport_introspection_cppExport.cmake", "/opt/ros/jazzy/share/builtin_interfaces/cmake/builtin_interfaces__rosidl_typesupport_introspection_cExport.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_cpp/cmake/rosidl_typesupport_cppExport.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_cpp/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/jazzy/share/rosidl_typesupport_cpp/cmake/rosidl_typesupport_cppConfig.cmake", "/opt/ros/jazzy/share/builtin_interfaces/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/jazzy/share/tracetools/cmake/tracetools_exportExport.cmake", "/opt/ros/jazzy/share/tracetools/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/jazzy/share/tracetools/cmake/tracetoolsConfig.cmake", "/opt/ros/jazzy/share/rcutils/cmake/rcutilsExport.cmake", "/opt/ros/jazzy/share/rcutils/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/jazzy/share/rcutils/cmake/rcutilsConfig.cmake", "/opt/ros/jazzy/share/rosidl_runtime_c/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/jazzy/share/rosidl_runtime_c/cmake/rosidl_runtime_cConfig.cmake"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 209, "parent": 0}, {"command": 0, "file": 0, "line": 74, "parent": 1}, {"command": 2, "file": 0, "line": 80, "parent": 1}, {"command": 3, "file": 0, "line": 75, "parent": 1}, {"command": 6, "file": 1, "line": 55, "parent": 0}, {"file": 8, "parent": 5}, {"command": 5, "file": 8, "line": 41, "parent": 6}, {"file": 7, "parent": 7}, {"command": 6, "file": 7, "line": 21, "parent": 8}, {"file": 6, "parent": 9}, {"command": 5, "file": 6, "line": 41, "parent": 10}, {"file": 5, "parent": 11}, {"command": 6, "file": 5, "line": 21, "parent": 12}, {"file": 4, "parent": 13}, {"command": 5, "file": 4, "line": 41, "parent": 14}, {"file": 3, "parent": 15}, {"command": 5, "file": 3, "line": 9, "parent": 16}, {"file": 2, "parent": 17}, {"command": 4, "file": 2, "line": 61, "parent": 18}, {"command": 6, "file": 1, "line": 55, "parent": 0}, {"file": 13, "parent": 20}, {"command": 5, "file": 13, "line": 41, "parent": 21}, {"file": 12, "parent": 22}, {"command": 6, "file": 12, "line": 21, "parent": 23}, {"file": 11, "parent": 24}, {"command": 5, "file": 11, "line": 41, "parent": 25}, {"file": 10, "parent": 26}, {"command": 5, "file": 10, "line": 9, "parent": 27}, {"file": 9, "parent": 28}, {"command": 4, "file": 9, "line": 61, "parent": 29}, {"command": 5, "file": 11, "line": 41, "parent": 25}, {"file": 19, "parent": 31}, {"command": 6, "file": 19, "line": 21, "parent": 32}, {"file": 18, "parent": 33}, {"command": 5, "file": 18, "line": 41, "parent": 34}, {"file": 17, "parent": 35}, {"command": 6, "file": 17, "line": 21, "parent": 36}, {"file": 16, "parent": 37}, {"command": 5, "file": 16, "line": 41, "parent": 38}, {"file": 15, "parent": 39}, {"command": 5, "file": 15, "line": 9, "parent": 40}, {"file": 14, "parent": 41}, {"command": 4, "file": 14, "line": 61, "parent": 42}, {"command": 5, "file": 16, "line": 41, "parent": 38}, {"file": 23, "parent": 44}, {"command": 6, "file": 23, "line": 21, "parent": 45}, {"file": 22, "parent": 46}, {"command": 5, "file": 22, "line": 41, "parent": 47}, {"file": 21, "parent": 48}, {"command": 5, "file": 21, "line": 9, "parent": 49}, {"file": 20, "parent": 50}, {"command": 4, "file": 20, "line": 61, "parent": 51}, {"command": 5, "file": 21, "line": 9, "parent": 49}, {"file": 24, "parent": 53}, {"command": 4, "file": 24, "line": 61, "parent": 54}, {"command": 5, "file": 21, "line": 9, "parent": 49}, {"file": 25, "parent": 56}, {"command": 4, "file": 25, "line": 61, "parent": 57}, {"command": 5, "file": 21, "line": 9, "parent": 49}, {"file": 26, "parent": 59}, {"command": 4, "file": 26, "line": 61, "parent": 60}, {"command": 5, "file": 21, "line": 9, "parent": 49}, {"file": 27, "parent": 62}, {"command": 4, "file": 27, "line": 61, "parent": 63}, {"command": 5, "file": 21, "line": 9, "parent": 49}, {"file": 28, "parent": 65}, {"command": 4, "file": 28, "line": 61, "parent": 66}, {"command": 5, "file": 21, "line": 9, "parent": 49}, {"file": 29, "parent": 68}, {"command": 4, "file": 29, "line": 61, "parent": 69}, {"command": 6, "file": 1, "line": 55, "parent": 0}, {"file": 32, "parent": 71}, {"command": 5, "file": 32, "line": 41, "parent": 72}, {"file": 31, "parent": 73}, {"command": 5, "file": 31, "line": 9, "parent": 74}, {"file": 30, "parent": 75}, {"command": 4, "file": 30, "line": 61, "parent": 76}, {"command": 5, "file": 31, "line": 9, "parent": 74}, {"file": 33, "parent": 78}, {"command": 4, "file": 33, "line": 61, "parent": 79}, {"command": 5, "file": 31, "line": 9, "parent": 74}, {"file": 34, "parent": 81}, {"command": 4, "file": 34, "line": 61, "parent": 82}, {"command": 5, "file": 32, "line": 41, "parent": 72}, {"file": 38, "parent": 84}, {"command": 6, "file": 38, "line": 21, "parent": 85}, {"file": 37, "parent": 86}, {"command": 5, "file": 37, "line": 41, "parent": 87}, {"file": 36, "parent": 88}, {"command": 5, "file": 36, "line": 9, "parent": 89}, {"file": 35, "parent": 90}, {"command": 4, "file": 35, "line": 61, "parent": 91}, {"command": 6, "file": 19, "line": 21, "parent": 32}, {"file": 41, "parent": 93}, {"command": 5, "file": 41, "line": 41, "parent": 94}, {"file": 40, "parent": 95}, {"command": 5, "file": 40, "line": 9, "parent": 96}, {"file": 39, "parent": 97}, {"command": 4, "file": 39, "line": 61, "parent": 98}, {"command": 6, "file": 38, "line": 21, "parent": 85}, {"file": 46, "parent": 100}, {"command": 5, "file": 46, "line": 41, "parent": 101}, {"file": 45, "parent": 102}, {"command": 6, "file": 45, "line": 21, "parent": 103}, {"file": 44, "parent": 104}, {"command": 5, "file": 44, "line": 41, "parent": 105}, {"file": 43, "parent": 106}, {"command": 5, "file": 43, "line": 9, "parent": 107}, {"file": 42, "parent": 108}, {"command": 4, "file": 42, "line": 61, "parent": 109}, {"command": 7, "file": 1, "line": 16, "parent": 0}, {"command": 8, "file": 1, "line": 20, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -fPIC -O3 -O3 -DNDEBUG -std=gnu++17"}, {"backtrace": 111, "fragment": "-Wall"}, {"backtrace": 111, "fragment": "-Wextra"}, {"backtrace": 111, "fragment": "-Werror"}, {"backtrace": 111, "fragment": "-Wno-pedantic"}, {"backtrace": 111, "fragment": "-Wno-array-bounds"}], "defines": [{"backtrace": 4, "define": "DEFAULT_RMW_IMPLEMENTATION=rmw_fastrtps_cpp"}, {"backtrace": 4, "define": "FASTCDR_DYN_LINK"}, {"backtrace": 112, "define": "ROS_JAZZY"}], "includes": [{"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/class_loader"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rcpputils"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rcutils"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rclcpp"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/builtin_interfaces"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_runtime_c"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_typesupport_interface"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/fastcdr"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_runtime_cpp"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_typesupport_fastrtps_cpp"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rmw"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_dynamic_typesupport"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_typesupport_fastrtps_c"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_typesupport_introspection_c"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_typesupport_introspection_cpp"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/libstatistics_collector"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rcl"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rcl_interfaces"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/service_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rcl_logging_interface"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rcl_yaml_param_parser"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/type_description_interfaces"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/statistics_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rosgraph_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_typesupport_cpp"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_typesupport_c"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/tracetools"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rclcpp_components"}], "language": "CXX", "languageStandard": {"backtraces": [4], "standard": "17"}, "sourceIndexes": [0]}], "id": "orbbec_camera_node::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 3, "path": "lib/orbbec_camera"}], "prefix": {"path": "/home/<USER>/ws_ros2/install/orbbec_camera"}}, "link": {"commandFragments": [{"fragment": "-fPIC -O3 -O3 -DNDEBUG", "role": "flags"}, {"fragment": "-Wl,--no-as-needed  /opt/ros/jazzy/lib/libbackward.so -Wl,--as-needed", "role": "flags"}, {"fragment": "-Wl,-r<PERSON>,:/home/<USER>/ws_ros2/src/Sensors_ROS2/OrbbecSDK_ROS2/orbbec_camera/SDK/lib/x64:/opt/ros/jazzy/lib:", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/jazzy/lib/libclass_loader.so", "role": "libraries"}, {"backtrace": 19, "fragment": "/usr/lib/x86_64-linux-gnu/libconsole_bridge.so.1.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/jazzy/lib/librclcpp.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/jazzy/lib/liblibstatistics_collector.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/jazzy/lib/librcl.so", "role": "libraries"}, {"backtrace": 43, "fragment": "/opt/ros/jazzy/lib/librmw_implementation.so", "role": "libraries"}, {"backtrace": 43, "fragment": "/opt/ros/jazzy/lib/libtype_description_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 43, "fragment": "/opt/ros/jazzy/lib/libtype_description_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 43, "fragment": "/opt/ros/jazzy/lib/libtype_description_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 43, "fragment": "/opt/ros/jazzy/lib/libtype_description_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 43, "fragment": "/opt/ros/jazzy/lib/libtype_description_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 43, "fragment": "/opt/ros/jazzy/lib/libtype_description_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 43, "fragment": "/opt/ros/jazzy/lib/libtype_description_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 43, "fragment": "/opt/ros/jazzy/lib/libtype_description_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/jazzy/lib/librcl_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/jazzy/lib/librcl_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/jazzy/lib/librcl_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/jazzy/lib/librcl_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/jazzy/lib/librcl_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/jazzy/lib/librcl_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/jazzy/lib/librcl_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/jazzy/lib/librcl_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 52, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 55, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 58, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 61, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 64, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 67, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 70, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/jazzy/lib/librcl_yaml_param_parser.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/jazzy/lib/librosgraph_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/jazzy/lib/librosgraph_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/jazzy/lib/librosgraph_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/jazzy/lib/librosgraph_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/jazzy/lib/librosgraph_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/jazzy/lib/librosgraph_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/jazzy/lib/librosgraph_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/jazzy/lib/librosgraph_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/jazzy/lib/libstatistics_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/jazzy/lib/libstatistics_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/jazzy/lib/libstatistics_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/jazzy/lib/libstatistics_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/jazzy/lib/libstatistics_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/jazzy/lib/libstatistics_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/jazzy/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 77, "fragment": "/opt/ros/jazzy/lib/librosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/jazzy/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/jazzy/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 77, "fragment": "/opt/ros/jazzy/lib/librosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/jazzy/lib/librmw.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/jazzy/lib/librosidl_dynamic_typesupport.so", "role": "libraries"}, {"backtrace": 77, "fragment": "/opt/ros/jazzy/lib/libfastcdr.so.2.2.5", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/jazzy/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 80, "fragment": "/opt/ros/jazzy/lib/librosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 83, "fragment": "/opt/ros/jazzy/lib/librosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/jazzy/lib/libbuiltin_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/jazzy/lib/librosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/jazzy/lib/libbuiltin_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/jazzy/lib/libstatistics_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/jazzy/lib/libbuiltin_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/jazzy/lib/libstatistics_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/jazzy/lib/libbuiltin_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 92, "fragment": "/opt/ros/jazzy/lib/librosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 19, "fragment": "/opt/ros/jazzy/lib/librcpputils.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/jazzy/lib/librosidl_runtime_c.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/jazzy/lib/libtracetools.so", "role": "libraries"}, {"backtrace": 99, "fragment": "-llttng-ust", "role": "libraries"}, {"backtrace": 99, "fragment": "-llttng-ust-common", "role": "libraries"}, {"backtrace": 99, "fragment": "-rdynamic", "role": "libraries"}, {"backtrace": 99, "fragment": "-ldl", "role": "libraries"}, {"backtrace": 43, "fragment": "/opt/ros/jazzy/lib/librcl_logging_interface.so", "role": "libraries"}, {"backtrace": 30, "fragment": "/opt/ros/jazzy/lib/librcutils.so", "role": "libraries"}, {"backtrace": 110, "fragment": "-ldl", "role": "libraries"}, {"fragment": "-Wl,-rpath-link,/opt/ros/jazzy/lib", "role": "libraries"}], "language": "CXX"}, "name": "orbbec_camera_node", "nameOnDisk": "orbbec_camera_node", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 2, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ws_ros2/build/orbbec_camera/rclcpp_components/node_main_orbbec_camera_node.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}