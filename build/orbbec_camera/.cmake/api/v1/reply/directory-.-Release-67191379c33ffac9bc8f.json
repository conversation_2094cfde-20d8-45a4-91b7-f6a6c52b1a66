{"backtraceGraph": {"commands": ["install", "rclcpp_components_register_node", "ament_environment_hooks", "_ament_cmake_export_libraries_register_environment_hook", "ament_export_libraries", "ament_index_register_resource", "ament_cmake_environment_generate_package_run_dependencies_marker", "include", "ament_execute_extensions", "ament_package", "ament_cmake_environment_generate_parent_prefix_path_marker", "ament_generate_package_environment", "ament_index_register_package", "_ament_package"], "files": ["/opt/ros/jazzy/share/rclcpp_components/cmake/rclcpp_components_register_node.cmake", "CMakeLists.txt", "/opt/ros/jazzy/share/ament_cmake_core/cmake/environment_hooks/ament_environment_hooks.cmake", "/opt/ros/jazzy/share/ament_cmake_export_libraries/cmake/ament_cmake_export_libraries-extras.cmake", "/opt/ros/jazzy/share/ament_cmake_export_libraries/cmake/ament_export_libraries.cmake", "/opt/ros/jazzy/share/ament_cmake_core/cmake/index/ament_index_register_resource.cmake", "/opt/ros/jazzy/share/ament_cmake_core/cmake/environment/ament_cmake_environment_package_hook.cmake", "/opt/ros/jazzy/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake", "/opt/ros/jazzy/share/ament_cmake_core/cmake/core/ament_package.cmake", "/opt/ros/jazzy/share/ament_cmake_core/cmake/environment_hooks/ament_cmake_environment_hooks_package_hook.cmake", "/opt/ros/jazzy/share/ament_cmake_core/cmake/environment_hooks/ament_generate_package_environment.cmake", "/opt/ros/jazzy/share/ament_cmake_core/cmake/index/ament_index_register_package.cmake", "/opt/ros/jazzy/share/ament_cmake_core/cmake/index/ament_cmake_index_package_hook.cmake", "/opt/ros/jazzy/share/rclcpp_components/cmake/rclcpp_components_package_hook.cmake"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 209, "parent": 0}, {"command": 0, "file": 0, "line": 80, "parent": 1}, {"command": 1, "file": 1, "line": 226, "parent": 0}, {"command": 0, "file": 0, "line": 80, "parent": 3}, {"command": 0, "file": 1, "line": 231, "parent": 0}, {"command": 0, "file": 1, "line": 237, "parent": 0}, {"command": 0, "file": 1, "line": 238, "parent": 0}, {"command": 0, "file": 1, "line": 239, "parent": 0}, {"command": 0, "file": 1, "line": 240, "parent": 0}, {"command": 0, "file": 1, "line": 241, "parent": 0}, {"command": 0, "file": 1, "line": 249, "parent": 0}, {"command": 4, "file": 1, "line": 261, "parent": 0}, {"command": 3, "file": 4, "line": 35, "parent": 12}, {"command": 2, "file": 3, "line": 25, "parent": 13}, {"command": 0, "file": 2, "line": 70, "parent": 14}, {"command": 0, "file": 2, "line": 87, "parent": 14}, {"command": 9, "file": 1, "line": 264, "parent": 0}, {"command": 8, "file": 8, "line": 66, "parent": 17}, {"command": 7, "file": 7, "line": 48, "parent": 18}, {"file": 6, "parent": 19}, {"command": 6, "file": 6, "line": 47, "parent": 20}, {"command": 5, "file": 6, "line": 29, "parent": 21}, {"command": 0, "file": 5, "line": 105, "parent": 22}, {"command": 10, "file": 6, "line": 48, "parent": 20}, {"command": 5, "file": 6, "line": 43, "parent": 24}, {"command": 0, "file": 5, "line": 105, "parent": 25}, {"command": 7, "file": 7, "line": 48, "parent": 18}, {"file": 9, "parent": 27}, {"command": 2, "file": 9, "line": 20, "parent": 28}, {"command": 0, "file": 2, "line": 70, "parent": 29}, {"command": 0, "file": 2, "line": 87, "parent": 29}, {"command": 0, "file": 2, "line": 70, "parent": 29}, {"command": 0, "file": 2, "line": 87, "parent": 29}, {"command": 11, "file": 9, "line": 26, "parent": 28}, {"command": 0, "file": 10, "line": 91, "parent": 34}, {"command": 0, "file": 10, "line": 91, "parent": 34}, {"command": 0, "file": 10, "line": 91, "parent": 34}, {"command": 0, "file": 10, "line": 107, "parent": 34}, {"command": 0, "file": 10, "line": 120, "parent": 34}, {"command": 7, "file": 7, "line": 48, "parent": 18}, {"file": 12, "parent": 40}, {"command": 12, "file": 12, "line": 16, "parent": 41}, {"command": 5, "file": 11, "line": 29, "parent": 42}, {"command": 0, "file": 5, "line": 105, "parent": 43}, {"command": 7, "file": 7, "line": 48, "parent": 18}, {"file": 13, "parent": 45}, {"command": 5, "file": 13, "line": 18, "parent": 46}, {"command": 0, "file": 5, "line": 105, "parent": 47}, {"command": 13, "file": 8, "line": 68, "parent": 17}, {"command": 0, "file": 8, "line": 122, "parent": 49}, {"command": 0, "file": 8, "line": 122, "parent": 49}, {"command": 0, "file": 8, "line": 122, "parent": 49}, {"command": 0, "file": 8, "line": 150, "parent": 49}, {"command": 0, "file": 8, "line": 157, "parent": 49}]}, "installers": [{"backtrace": 2, "component": "Unspecified", "destination": "lib/orbbec_camera", "paths": ["orbbec_camera_node"], "targetId": "orbbec_camera_node::@6890427a1f51a3e7e1df", "targetIndex": 6, "type": "target"}, {"backtrace": 2, "component": "Unspecified", "cxxModuleBmiTarget": {"id": "orbbec_camera_node::@6890427a1f51a3e7e1df", "index": 6}, "destination": "lib/orbbec_camera", "type": "cxxModuleBmi"}, {"backtrace": 4, "component": "Unspecified", "destination": "lib/orbbec_camera", "paths": ["frame_latency_node"], "targetId": "frame_latency_node::@6890427a1f51a3e7e1df", "targetIndex": 1, "type": "target"}, {"backtrace": 4, "component": "Unspecified", "cxxModuleBmiTarget": {"id": "frame_latency_node::@6890427a1f51a3e7e1df", "index": 1}, "destination": "lib/orbbec_camera", "type": "cxxModuleBmi"}, {"backtrace": 5, "component": "Unspecified", "destination": "lib", "paths": ["liborbbec_camera.so"], "targetId": "orbbec_camera::@6890427a1f51a3e7e1df", "targetIndex": 5, "type": "target"}, {"backtrace": 5, "component": "Unspecified", "destination": "lib", "paths": ["libframe_latency.so"], "targetId": "frame_latency::@6890427a1f51a3e7e1df", "targetIndex": 0, "type": "target"}, {"backtrace": 6, "component": "Unspecified", "destination": "include", "paths": [{"from": "include", "to": "."}], "type": "directory"}, {"backtrace": 7, "component": "Unspecified", "destination": "share/orbbec_camera/", "paths": ["launch"], "type": "directory"}, {"backtrace": 8, "component": "Unspecified", "destination": "share/orbbec_camera/", "paths": ["config"], "type": "directory"}, {"backtrace": 9, "component": "Unspecified", "destination": "include", "paths": [{"from": "SDK/include", "to": "."}], "type": "directory"}, {"backtrace": 10, "component": "Unspecified", "destination": "lib/", "paths": [{"from": "SDK/lib/x64", "to": "."}], "type": "directory"}, {"backtrace": 11, "component": "Unspecified", "destination": "lib/orbbec_camera", "paths": ["list_devices_node"], "targetId": "list_devices_node::@6890427a1f51a3e7e1df", "targetIndex": 4, "type": "target"}, {"backtrace": 11, "component": "Unspecified", "cxxModuleBmiTarget": {"id": "list_devices_node::@6890427a1f51a3e7e1df", "index": 4}, "destination": "lib/orbbec_camera", "type": "cxxModuleBmi"}, {"backtrace": 11, "component": "Unspecified", "destination": "lib/orbbec_camera", "paths": ["list_depth_work_mode_node"], "targetId": "list_depth_work_mode_node::@6890427a1f51a3e7e1df", "targetIndex": 3, "type": "target"}, {"backtrace": 11, "component": "Unspecified", "cxxModuleBmiTarget": {"id": "list_depth_work_mode_node::@6890427a1f51a3e7e1df", "index": 3}, "destination": "lib/orbbec_camera", "type": "cxxModuleBmi"}, {"backtrace": 11, "component": "Unspecified", "destination": "lib/orbbec_camera", "paths": ["list_camera_profile_mode_node"], "targetId": "list_camera_profile_mode_node::@6890427a1f51a3e7e1df", "targetIndex": 2, "type": "target"}, {"backtrace": 11, "component": "Unspecified", "cxxModuleBmiTarget": {"id": "list_camera_profile_mode_node::@6890427a1f51a3e7e1df", "index": 2}, "destination": "lib/orbbec_camera", "type": "cxxModuleBmi"}, {"backtrace": 11, "component": "Unspecified", "destination": "lib/orbbec_camera", "paths": ["topic_statistics_node"], "targetId": "topic_statistics_node::@6890427a1f51a3e7e1df", "targetIndex": 8, "type": "target"}, {"backtrace": 11, "component": "Unspecified", "cxxModuleBmiTarget": {"id": "topic_statistics_node::@6890427a1f51a3e7e1df", "index": 8}, "destination": "lib/orbbec_camera", "type": "cxxModuleBmi"}, {"backtrace": 15, "component": "Unspecified", "destination": "share/orbbec_camera/environment", "paths": ["/opt/ros/jazzy/lib/python3.12/site-packages/ament_package/template/environment_hook/library_path.sh"], "type": "file"}, {"backtrace": 16, "component": "Unspecified", "destination": "share/orbbec_camera/environment", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera/ament_cmake_environment_hooks/library_path.dsv"], "type": "file"}, {"backtrace": 23, "component": "Unspecified", "destination": "share/ament_index/resource_index/package_run_dependencies", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera/ament_cmake_index/share/ament_index/resource_index/package_run_dependencies/orbbec_camera"], "type": "file"}, {"backtrace": 26, "component": "Unspecified", "destination": "share/ament_index/resource_index/parent_prefix_path", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera/ament_cmake_index/share/ament_index/resource_index/parent_prefix_path/orbbec_camera"], "type": "file"}, {"backtrace": 30, "component": "Unspecified", "destination": "share/orbbec_camera/environment", "paths": ["/opt/ros/jazzy/share/ament_cmake_core/cmake/environment_hooks/environment/ament_prefix_path.sh"], "type": "file"}, {"backtrace": 31, "component": "Unspecified", "destination": "share/orbbec_camera/environment", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera/ament_cmake_environment_hooks/ament_prefix_path.dsv"], "type": "file"}, {"backtrace": 32, "component": "Unspecified", "destination": "share/orbbec_camera/environment", "paths": ["/opt/ros/jazzy/share/ament_cmake_core/cmake/environment_hooks/environment/path.sh"], "type": "file"}, {"backtrace": 33, "component": "Unspecified", "destination": "share/orbbec_camera/environment", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera/ament_cmake_environment_hooks/path.dsv"], "type": "file"}, {"backtrace": 35, "component": "Unspecified", "destination": "share/orbbec_camera", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera/ament_cmake_environment_hooks/local_setup.bash"], "type": "file"}, {"backtrace": 36, "component": "Unspecified", "destination": "share/orbbec_camera", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera/ament_cmake_environment_hooks/local_setup.sh"], "type": "file"}, {"backtrace": 37, "component": "Unspecified", "destination": "share/orbbec_camera", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera/ament_cmake_environment_hooks/local_setup.zsh"], "type": "file"}, {"backtrace": 38, "component": "Unspecified", "destination": "share/orbbec_camera", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera/ament_cmake_environment_hooks/local_setup.dsv"], "type": "file"}, {"backtrace": 39, "component": "Unspecified", "destination": "share/orbbec_camera", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera/ament_cmake_environment_hooks/package.dsv"], "type": "file"}, {"backtrace": 44, "component": "Unspecified", "destination": "share/ament_index/resource_index/packages", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera/ament_cmake_index/share/ament_index/resource_index/packages/orbbec_camera"], "type": "file"}, {"backtrace": 48, "component": "Unspecified", "destination": "share/ament_index/resource_index/rclcpp_components", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera/ament_cmake_index/share/ament_index/resource_index/rclcpp_components/orbbec_camera"], "type": "file"}, {"backtrace": 50, "component": "Unspecified", "destination": "share/orbbec_camera/cmake", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera/ament_cmake_export_include_directories/ament_cmake_export_include_directories-extras.cmake"], "type": "file"}, {"backtrace": 51, "component": "Unspecified", "destination": "share/orbbec_camera/cmake", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera/ament_cmake_export_libraries/ament_cmake_export_libraries-extras.cmake"], "type": "file"}, {"backtrace": 52, "component": "Unspecified", "destination": "share/orbbec_camera/cmake", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera/ament_cmake_export_dependencies/ament_cmake_export_dependencies-extras.cmake"], "type": "file"}, {"backtrace": 53, "component": "Unspecified", "destination": "share/orbbec_camera/cmake", "paths": ["/home/<USER>/ws_ros2/build/orbbec_camera/ament_cmake_core/orbbec_cameraConfig.cmake", "/home/<USER>/ws_ros2/build/orbbec_camera/ament_cmake_core/orbbec_cameraConfig-version.cmake"], "type": "file"}, {"backtrace": 54, "component": "Unspecified", "destination": "share/orbbec_camera", "paths": ["package.xml"], "type": "file"}], "paths": {"build": ".", "source": "."}}