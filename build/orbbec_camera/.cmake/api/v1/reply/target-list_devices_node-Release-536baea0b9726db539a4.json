{"artifacts": [{"path": "list_devices_node"}], "backtrace": 2, "backtraceGraph": {"commands": ["add_executable", "add_orbbec_executable", "install", "target_link_libraries", "ament_target_dependencies", "set_target_properties", "include", "find_package", "add_compile_options", "add_compile_definitions", "target_include_directories"], "files": ["CMakeLists.txt", "/opt/ros/jazzy/share/ament_cmake_target_dependencies/cmake/ament_target_dependencies.cmake", "/opt/ros/jazzy/share/tf2_ros/cmake/export_tf2_rosExport.cmake", "/opt/ros/jazzy/share/tf2_ros/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/jazzy/share/tf2_ros/cmake/tf2_rosConfig.cmake", "/opt/ros/jazzy/share/tf2_eigen/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/jazzy/share/tf2_eigen/cmake/tf2_eigenConfig.cmake", "/opt/ros/jazzy/share/rclcpp_action/cmake/rclcpp_actionExport.cmake", "/opt/ros/jazzy/share/rclcpp_action/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/jazzy/share/rclcpp_action/cmake/rclcpp_actionConfig.cmake", "/opt/ros/jazzy/share/tf2_ros/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/jazzy/share/tf2_msgs/cmake/export_tf2_msgs__rosidl_typesupport_fastrtps_cExport.cmake", "/opt/ros/jazzy/share/tf2_msgs/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/jazzy/share/tf2_msgs/cmake/tf2_msgsConfig.cmake", "/opt/ros/jazzy/share/tf2_msgs/cmake/tf2_msgs__rosidl_typesupport_introspection_cExport.cmake", "/opt/ros/jazzy/share/tf2_msgs/cmake/export_tf2_msgs__rosidl_typesupport_fastrtps_cppExport.cmake", "/opt/ros/jazzy/share/tf2_msgs/cmake/tf2_msgs__rosidl_typesupport_introspection_cppExport.cmake", "/opt/ros/jazzy/share/tf2_msgs/cmake/tf2_msgs__rosidl_typesupport_cppExport.cmake", "/opt/ros/jazzy/share/tf2_msgs/cmake/tf2_msgs__rosidl_typesupport_cExport.cmake", "/opt/ros/jazzy/share/tf2_msgs/cmake/export_tf2_msgs__rosidl_generator_cExport.cmake", "/home/<USER>/ws_ros2/install/orbbec_camera_msgs/share/orbbec_camera_msgs/cmake/export_orbbec_camera_msgs__rosidl_typesupport_fastrtps_cExport.cmake", "/home/<USER>/ws_ros2/install/orbbec_camera_msgs/share/orbbec_camera_msgs/cmake/ament_cmake_export_targets-extras.cmake", "/home/<USER>/ws_ros2/install/orbbec_camera_msgs/share/orbbec_camera_msgs/cmake/orbbec_camera_msgsConfig.cmake", "/home/<USER>/ws_ros2/install/orbbec_camera_msgs/share/orbbec_camera_msgs/cmake/orbbec_camera_msgs__rosidl_typesupport_introspection_cExport.cmake", "/home/<USER>/ws_ros2/install/orbbec_camera_msgs/share/orbbec_camera_msgs/cmake/export_orbbec_camera_msgs__rosidl_typesupport_fastrtps_cppExport.cmake", "/home/<USER>/ws_ros2/install/orbbec_camera_msgs/share/orbbec_camera_msgs/cmake/orbbec_camera_msgs__rosidl_typesupport_introspection_cppExport.cmake", "/home/<USER>/ws_ros2/install/orbbec_camera_msgs/share/orbbec_camera_msgs/cmake/orbbec_camera_msgs__rosidl_typesupport_cppExport.cmake", "/opt/ros/jazzy/share/tf2/cmake/export_tf2Export.cmake", "/opt/ros/jazzy/share/tf2/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/jazzy/share/tf2/cmake/tf2Config.cmake", "/home/<USER>/ws_ros2/install/orbbec_camera_msgs/share/orbbec_camera_msgs/cmake/orbbec_camera_msgs__rosidl_typesupport_cExport.cmake", "/home/<USER>/ws_ros2/install/orbbec_camera_msgs/share/orbbec_camera_msgs/cmake/export_orbbec_camera_msgs__rosidl_generator_cExport.cmake", "/opt/ros/jazzy/share/rclcpp_lifecycle/cmake/rclcpp_lifecycleExport.cmake", "/opt/ros/jazzy/share/rclcpp_lifecycle/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/jazzy/share/rclcpp_lifecycle/cmake/rclcpp_lifecycleConfig.cmake", "/opt/ros/jazzy/share/camera_info_manager/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/jazzy/share/camera_info_manager/cmake/camera_info_managerConfig.cmake", "/opt/ros/jazzy/share/rcl/cmake/rclExport.cmake", "/opt/ros/jazzy/share/rcl/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/jazzy/share/rcl/cmake/rclConfig.cmake", "/opt/ros/jazzy/share/libstatistics_collector/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/jazzy/share/libstatistics_collector/cmake/libstatistics_collectorConfig.cmake", "/opt/ros/jazzy/share/rclcpp/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/jazzy/share/rclcpp/cmake/rclcppConfig.cmake", "/opt/ros/jazzy/share/cv_bridge/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/jazzy/share/cv_bridge/cmake/cv_bridgeConfig.cmake", "/opt/ros/jazzy/share/tracetools/cmake/tracetools_exportExport.cmake", "/opt/ros/jazzy/share/tracetools/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/jazzy/share/tracetools/cmake/tracetoolsConfig.cmake", "/opt/ros/jazzy/share/class_loader/cmake/class_loaderExport.cmake", "/opt/ros/jazzy/share/class_loader/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/jazzy/share/class_loader/cmake/class_loaderConfig.cmake", "/opt/ros/jazzy/share/pluginlib/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/jazzy/share/pluginlib/cmake/pluginlibConfig.cmake", "/opt/ros/jazzy/share/image_transport/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/jazzy/share/image_transport/cmake/image_transportConfig.cmake"], "nodes": [{"file": 0}, {"command": 1, "file": 0, "line": 214, "parent": 0}, {"command": 0, "file": 0, "line": 193, "parent": 1}, {"command": 2, "file": 0, "line": 249, "parent": 0}, {"command": 3, "file": 0, "line": 195, "parent": 1}, {"command": 4, "file": 0, "line": 196, "parent": 1}, {"command": 3, "file": 1, "line": 146, "parent": 5}, {"command": 3, "file": 1, "line": 152, "parent": 5}, {"command": 7, "file": 0, "line": 55, "parent": 0}, {"file": 6, "parent": 8}, {"command": 6, "file": 6, "line": 41, "parent": 9}, {"file": 5, "parent": 10}, {"command": 7, "file": 5, "line": 21, "parent": 11}, {"file": 4, "parent": 12}, {"command": 6, "file": 4, "line": 41, "parent": 13}, {"file": 3, "parent": 14}, {"command": 6, "file": 3, "line": 9, "parent": 15}, {"file": 2, "parent": 16}, {"command": 5, "file": 2, "line": 61, "parent": 17}, {"command": 6, "file": 4, "line": 41, "parent": 13}, {"file": 10, "parent": 19}, {"command": 7, "file": 10, "line": 21, "parent": 20}, {"file": 9, "parent": 21}, {"command": 6, "file": 9, "line": 41, "parent": 22}, {"file": 8, "parent": 23}, {"command": 6, "file": 8, "line": 9, "parent": 24}, {"file": 7, "parent": 25}, {"command": 5, "file": 7, "line": 61, "parent": 26}, {"command": 7, "file": 10, "line": 21, "parent": 20}, {"file": 13, "parent": 28}, {"command": 6, "file": 13, "line": 41, "parent": 29}, {"file": 12, "parent": 30}, {"command": 6, "file": 12, "line": 9, "parent": 31}, {"file": 11, "parent": 32}, {"command": 5, "file": 11, "line": 61, "parent": 33}, {"command": 6, "file": 12, "line": 9, "parent": 31}, {"file": 14, "parent": 35}, {"command": 5, "file": 14, "line": 61, "parent": 36}, {"command": 6, "file": 12, "line": 9, "parent": 31}, {"file": 15, "parent": 38}, {"command": 5, "file": 15, "line": 61, "parent": 39}, {"command": 6, "file": 12, "line": 9, "parent": 31}, {"file": 16, "parent": 41}, {"command": 5, "file": 16, "line": 61, "parent": 42}, {"command": 6, "file": 12, "line": 9, "parent": 31}, {"file": 17, "parent": 44}, {"command": 5, "file": 17, "line": 61, "parent": 45}, {"command": 6, "file": 12, "line": 9, "parent": 31}, {"file": 18, "parent": 47}, {"command": 5, "file": 18, "line": 61, "parent": 48}, {"command": 6, "file": 12, "line": 9, "parent": 31}, {"file": 19, "parent": 50}, {"command": 5, "file": 19, "line": 61, "parent": 51}, {"command": 7, "file": 0, "line": 55, "parent": 0}, {"file": 22, "parent": 53}, {"command": 6, "file": 22, "line": 41, "parent": 54}, {"file": 21, "parent": 55}, {"command": 6, "file": 21, "line": 9, "parent": 56}, {"file": 20, "parent": 57}, {"command": 5, "file": 20, "line": 61, "parent": 58}, {"command": 6, "file": 21, "line": 9, "parent": 56}, {"file": 23, "parent": 60}, {"command": 5, "file": 23, "line": 61, "parent": 61}, {"command": 6, "file": 21, "line": 9, "parent": 56}, {"file": 24, "parent": 63}, {"command": 5, "file": 24, "line": 61, "parent": 64}, {"command": 6, "file": 21, "line": 9, "parent": 56}, {"file": 25, "parent": 66}, {"command": 5, "file": 25, "line": 61, "parent": 67}, {"command": 6, "file": 21, "line": 9, "parent": 56}, {"file": 26, "parent": 69}, {"command": 5, "file": 26, "line": 61, "parent": 70}, {"command": 7, "file": 0, "line": 55, "parent": 0}, {"file": 29, "parent": 72}, {"command": 6, "file": 29, "line": 41, "parent": 73}, {"file": 28, "parent": 74}, {"command": 6, "file": 28, "line": 9, "parent": 75}, {"file": 27, "parent": 76}, {"command": 5, "file": 27, "line": 61, "parent": 77}, {"command": 6, "file": 21, "line": 9, "parent": 56}, {"file": 30, "parent": 79}, {"command": 5, "file": 30, "line": 61, "parent": 80}, {"command": 6, "file": 21, "line": 9, "parent": 56}, {"file": 31, "parent": 82}, {"command": 5, "file": 31, "line": 61, "parent": 83}, {"command": 7, "file": 0, "line": 55, "parent": 0}, {"file": 36, "parent": 85}, {"command": 6, "file": 36, "line": 41, "parent": 86}, {"file": 35, "parent": 87}, {"command": 7, "file": 35, "line": 21, "parent": 88}, {"file": 34, "parent": 89}, {"command": 6, "file": 34, "line": 41, "parent": 90}, {"file": 33, "parent": 91}, {"command": 6, "file": 33, "line": 9, "parent": 92}, {"file": 32, "parent": 93}, {"command": 5, "file": 32, "line": 61, "parent": 94}, {"command": 7, "file": 0, "line": 55, "parent": 0}, {"file": 45, "parent": 96}, {"command": 6, "file": 45, "line": 41, "parent": 97}, {"file": 44, "parent": 98}, {"command": 7, "file": 44, "line": 21, "parent": 99}, {"file": 43, "parent": 100}, {"command": 6, "file": 43, "line": 41, "parent": 101}, {"file": 42, "parent": 102}, {"command": 7, "file": 42, "line": 21, "parent": 103}, {"file": 41, "parent": 104}, {"command": 6, "file": 41, "line": 41, "parent": 105}, {"file": 40, "parent": 106}, {"command": 7, "file": 40, "line": 21, "parent": 107}, {"file": 39, "parent": 108}, {"command": 6, "file": 39, "line": 41, "parent": 109}, {"file": 38, "parent": 110}, {"command": 6, "file": 38, "line": 9, "parent": 111}, {"file": 37, "parent": 112}, {"command": 5, "file": 37, "line": 61, "parent": 113}, {"command": 7, "file": 42, "line": 21, "parent": 103}, {"file": 48, "parent": 115}, {"command": 6, "file": 48, "line": 41, "parent": 116}, {"file": 47, "parent": 117}, {"command": 6, "file": 47, "line": 9, "parent": 118}, {"file": 46, "parent": 119}, {"command": 5, "file": 46, "line": 61, "parent": 120}, {"command": 7, "file": 0, "line": 55, "parent": 0}, {"file": 55, "parent": 122}, {"command": 6, "file": 55, "line": 41, "parent": 123}, {"file": 54, "parent": 124}, {"command": 7, "file": 54, "line": 21, "parent": 125}, {"file": 53, "parent": 126}, {"command": 6, "file": 53, "line": 41, "parent": 127}, {"file": 52, "parent": 128}, {"command": 7, "file": 52, "line": 21, "parent": 129}, {"file": 51, "parent": 130}, {"command": 6, "file": 51, "line": 41, "parent": 131}, {"file": 50, "parent": 132}, {"command": 6, "file": 50, "line": 9, "parent": 133}, {"file": 49, "parent": 134}, {"command": 5, "file": 49, "line": 61, "parent": 135}, {"command": 8, "file": 0, "line": 16, "parent": 0}, {"command": 9, "file": 0, "line": 20, "parent": 0}, {"command": 10, "file": 0, "line": 194, "parent": 1}, {"command": 10, "file": 1, "line": 142, "parent": 5}, {"command": 10, "file": 1, "line": 148, "parent": 5}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -fPIC -O3 -O3 -DNDEBUG -std=gnu++17"}, {"backtrace": 137, "fragment": "-Wall"}, {"backtrace": 137, "fragment": "-Wextra"}, {"backtrace": 137, "fragment": "-Werror"}, {"backtrace": 137, "fragment": "-Wno-pedantic"}, {"backtrace": 137, "fragment": "-Wno-array-bounds"}], "defines": [{"backtrace": 4, "define": "DEFAULT_RMW_IMPLEMENTATION=rmw_fastrtps_cpp"}, {"backtrace": 4, "define": "FASTCDR_DYN_LINK"}, {"backtrace": 138, "define": "ROS_JAZZY"}], "includes": [{"backtrace": 139, "path": "/home/<USER>/ws_ros2/build/orbbec_camera/include"}, {"backtrace": 139, "path": "/home/<USER>/ws_ros2/src/Sensors_ROS2/OrbbecSDK_ROS2/orbbec_camera/include"}, {"backtrace": 139, "path": "/home/<USER>/ws_ros2/src/Sensors_ROS2/OrbbecSDK_ROS2/orbbec_camera/SDK/include"}, {"backtrace": 139, "path": "/home/<USER>/ws_ros2/src/Sensors_ROS2/OrbbecSDK_ROS2/orbbec_camera/tools"}, {"backtrace": 140, "isSystem": true, "path": "/home/<USER>/ws_ros2/install/orbbec_camera_msgs/include/orbbec_camera_msgs"}, {"backtrace": 140, "isSystem": true, "path": "/opt/ros/jazzy/include/ament_index_cpp"}, {"backtrace": 140, "isSystem": true, "path": "/opt/ros/jazzy/include/builtin_interfaces"}, {"backtrace": 140, "isSystem": true, "path": "/opt/ros/jazzy/include/cv_bridge"}, {"backtrace": 140, "isSystem": true, "path": "/opt/ros/jazzy/include/camera_info_manager"}, {"backtrace": 140, "isSystem": true, "path": "/opt/ros/jazzy/include/image_transport"}, {"backtrace": 140, "isSystem": true, "path": "/opt/ros/jazzy/include/rclcpp"}, {"backtrace": 140, "isSystem": true, "path": "/opt/ros/jazzy/include/rclcpp_components"}, {"backtrace": 140, "isSystem": true, "path": "/opt/ros/jazzy/include/sensor_msgs"}, {"backtrace": 140, "isSystem": true, "path": "/opt/ros/jazzy/include/std_msgs"}, {"backtrace": 140, "isSystem": true, "path": "/opt/ros/jazzy/include/std_srvs"}, {"backtrace": 140, "isSystem": true, "path": "/opt/ros/jazzy/include/tf2"}, {"backtrace": 140, "isSystem": true, "path": "/opt/ros/jazzy/include/tf2_eigen"}, {"backtrace": 140, "isSystem": true, "path": "/opt/ros/jazzy/include/tf2_msgs"}, {"backtrace": 140, "isSystem": true, "path": "/opt/ros/jazzy/include/tf2_ros"}, {"backtrace": 140, "isSystem": true, "path": "/opt/ros/jazzy/include/tf2_sensor_msgs"}, {"backtrace": 140, "isSystem": true, "path": "/opt/ros/jazzy/include"}, {"backtrace": 140, "isSystem": true, "path": "/opt/ros/jazzy/include/diagnostic_msgs"}, {"backtrace": 140, "isSystem": true, "path": "/opt/ros/jazzy/include/statistics_msgs"}, {"backtrace": 141, "isSystem": true, "path": "/opt/ros/jazzy/include/rcl_interfaces"}, {"backtrace": 141, "isSystem": true, "path": "/usr/include/opencv4"}, {"backtrace": 4, "isSystem": true, "path": "/usr/include/eigen3"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_runtime_c"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rcutils"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_typesupport_interface"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/fastcdr"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_runtime_cpp"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_typesupport_fastrtps_cpp"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rmw"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_dynamic_typesupport"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_typesupport_fastrtps_c"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_typesupport_introspection_c"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_typesupport_introspection_cpp"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/geometry_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/service_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/libstatistics_collector"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rcl"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rcl_logging_interface"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rcl_yaml_param_parser"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/type_description_interfaces"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rcpputils"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rosgraph_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_typesupport_cpp"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rosidl_typesupport_c"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/tracetools"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rclcpp_lifecycle"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/lifecycle_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rcl_lifecycle"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/message_filters"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/class_loader"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/composition_interfaces"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rclcpp_action"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/action_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/unique_identifier_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/jazzy/include/rcl_action"}], "language": "CXX", "languageStandard": {"backtraces": [4, 4], "standard": "17"}, "sourceIndexes": [0]}], "dependencies": [{"backtrace": 4, "id": "orbbec_camera::@6890427a1f51a3e7e1df"}], "id": "list_devices_node::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 3, "path": "lib/orbbec_camera"}], "prefix": {"path": "/home/<USER>/ws_ros2/install/orbbec_camera"}}, "link": {"commandFragments": [{"fragment": "-fPIC -O3 -O3 -DNDEBUG", "role": "flags"}, {"fragment": "-Wl,--no-as-needed  /opt/ros/jazzy/lib/libbackward.so -Wl,--as-needed", "role": "flags"}, {"fragment": "-Wl,-rpath,:/home/<USER>/ws_ros2/src/Sensors_ROS2/OrbbecSDK_ROS2/orbbec_camera/SDK/lib/x64:/home/<USER>/ws_ros2/build/orbbec_camera:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/lib:/home/<USER>/ws_ros2/install/orbbec_camera_msgs/lib:", "role": "libraries"}, {"backtrace": 4, "fragment": "-lOrbbecSDK", "role": "libraries"}, {"backtrace": 4, "fragment": "-L/home/<USER>/ws_ros2/src/Sensors_ROS2/OrbbecSDK_ROS2/orbbec_camera/SDK/lib/x64", "role": "libraries"}, {"backtrace": 4, "fragment": "-lrt", "role": "libraries"}, {"backtrace": 4, "fragment": "-ldw", "role": "libraries"}, {"backtrace": 4, "fragment": "liborbbec_camera.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libcv_bridge.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libcamera_info_manager.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/x86_64-linux-gnu/libimage_transport.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/home/<USER>/ws_ros2/install/orbbec_camera_msgs/lib/liborbbec_camera_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/home/<USER>/ws_ros2/install/orbbec_camera_msgs/lib/liborbbec_camera_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/home/<USER>/ws_ros2/install/orbbec_camera_msgs/lib/liborbbec_camera_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/home/<USER>/ws_ros2/install/orbbec_camera_msgs/lib/liborbbec_camera_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/home/<USER>/ws_ros2/install/orbbec_camera_msgs/lib/liborbbec_camera_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/home/<USER>/ws_ros2/install/orbbec_camera_msgs/lib/liborbbec_camera_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libcomponent_manager.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libstd_srvs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libstd_srvs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libstd_srvs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libstd_srvs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libstd_srvs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libstd_srvs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libstatic_transform_broadcaster_node.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libdiagnostic_updater.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libdiagnostic_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libdiagnostic_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libdiagnostic_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libdiagnostic_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libdiagnostic_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libdiagnostic_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libcamera_info_manager.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/librclcpp_lifecycle.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libcv_bridge.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/x86_64-linux-gnu/libimage_transport.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/librcl_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/librcl_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/librcl_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/librcl_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/librcl_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/librcl_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/librcl_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/librcl_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/librosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/librmw.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/librosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/librcutils.so", "role": "libraries"}, {"backtrace": 7, "fragment": "-ldl", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/librcpputils.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/librosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/librosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/librosidl_runtime_c.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/librosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/librosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libcomposition_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libcomposition_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libcomposition_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libcomposition_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libcomposition_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libcomposition_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libcomposition_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libcomposition_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/librclcpp.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libclass_loader.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libcomponent_manager.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libcomposition_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libcomposition_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libcomposition_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libcomposition_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libcomposition_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libcomposition_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libimage_publisher.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_stitching.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_alphamat.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_aruco.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_barcode.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_bgsegm.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_bioinspired.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_ccalib.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_cvv.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_dnn_objdetect.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_dnn_superres.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_dpm.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_face.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_freetype.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_fuzzy.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_hdf.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_hfs.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_img_hash.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_intensity_transform.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_line_descriptor.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_mcc.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_quality.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_rapid.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_reg.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_rgbd.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_saliency.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_shape.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_stereo.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_structured_light.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_superres.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_surface_matching.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_tracking.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_videostab.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_viz.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_wechat_qrcode.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_xobjdetect.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_xphoto.so.4.6.0", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libbackward.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_phase_unwrapping.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_optflow.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_highgui.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_datasets.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_plot.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_text.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_ml.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_videoio.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_ximgproc.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_video.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_objdetect.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_calib3d.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_dnn.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_features2d.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_flann.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_photo.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "-lOrbbecSDK", "role": "libraries"}, {"backtrace": 4, "fragment": "-L/home/<USER>/ws_ros2/src/Sensors_ROS2/OrbbecSDK_ROS2/orbbec_camera/SDK/lib/x64", "role": "libraries"}, {"backtrace": 4, "fragment": "-lrt", "role": "libraries"}, {"backtrace": 4, "fragment": "-ldw", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_imgcodecs.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_imgproc.so.4.6.0", "role": "libraries"}, {"backtrace": 4, "fragment": "/usr/lib/x86_64-linux-gnu/libopencv_core.so.4.6.0", "role": "libraries"}, {"backtrace": 6, "fragment": "/home/<USER>/ws_ros2/install/orbbec_camera_msgs/lib/liborbbec_camera_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/home/<USER>/ws_ros2/install/orbbec_camera_msgs/lib/liborbbec_camera_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libstd_srvs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libstd_srvs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libsensor_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libsensor_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libsensor_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libsensor_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libsensor_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libsensor_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libsensor_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libsensor_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libtf2_ros.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libtf2.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libtf2_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libtf2_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libtf2_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libtf2_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libtf2_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libtf2_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libtf2_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libtf2_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 18, "fragment": "/opt/ros/jazzy/lib/librclcpp_action.so", "role": "libraries"}, {"backtrace": 27, "fragment": "/opt/ros/jazzy/lib/librcl_action.so", "role": "libraries"}, {"backtrace": 34, "fragment": "/opt/ros/jazzy/lib/libaction_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 37, "fragment": "/opt/ros/jazzy/lib/libaction_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 40, "fragment": "/opt/ros/jazzy/lib/libaction_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 43, "fragment": "/opt/ros/jazzy/lib/libaction_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 46, "fragment": "/opt/ros/jazzy/lib/libaction_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 27, "fragment": "/opt/ros/jazzy/lib/libaction_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 34, "fragment": "/opt/ros/jazzy/lib/libunique_identifier_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 37, "fragment": "/opt/ros/jazzy/lib/libunique_identifier_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 49, "fragment": "/opt/ros/jazzy/lib/libaction_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 52, "fragment": "/opt/ros/jazzy/lib/libaction_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 40, "fragment": "/opt/ros/jazzy/lib/libunique_identifier_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 43, "fragment": "/opt/ros/jazzy/lib/libunique_identifier_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 46, "fragment": "/opt/ros/jazzy/lib/libunique_identifier_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 49, "fragment": "/opt/ros/jazzy/lib/libunique_identifier_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 52, "fragment": "/opt/ros/jazzy/lib/libunique_identifier_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 59, "fragment": "/opt/ros/jazzy/lib/libgeometry_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 62, "fragment": "/opt/ros/jazzy/lib/libgeometry_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 65, "fragment": "/opt/ros/jazzy/lib/libgeometry_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 68, "fragment": "/opt/ros/jazzy/lib/libgeometry_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 71, "fragment": "/opt/ros/jazzy/lib/libgeometry_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 78, "fragment": "/opt/ros/jazzy/lib/libgeometry_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 81, "fragment": "/opt/ros/jazzy/lib/libgeometry_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 84, "fragment": "/opt/ros/jazzy/lib/libgeometry_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libdiagnostic_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libdiagnostic_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 95, "fragment": "/opt/ros/jazzy/lib/librcl_lifecycle.so", "role": "libraries"}, {"backtrace": 95, "fragment": "/opt/ros/jazzy/lib/liblifecycle_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 95, "fragment": "/opt/ros/jazzy/lib/liblifecycle_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 95, "fragment": "/opt/ros/jazzy/lib/liblifecycle_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 95, "fragment": "/opt/ros/jazzy/lib/liblifecycle_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 95, "fragment": "/opt/ros/jazzy/lib/liblifecycle_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 95, "fragment": "/opt/ros/jazzy/lib/liblifecycle_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 95, "fragment": "/opt/ros/jazzy/lib/liblifecycle_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 95, "fragment": "/opt/ros/jazzy/lib/liblifecycle_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libmessage_filters.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/librclcpp.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libstd_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libstd_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libstd_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libstd_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libstd_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libstd_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libstd_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libstd_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/liblibstatistics_collector.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libstatistics_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libstatistics_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libstatistics_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libstatistics_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libstatistics_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libstatistics_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libstatistics_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libstatistics_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/librcl.so", "role": "libraries"}, {"backtrace": 114, "fragment": "/opt/ros/jazzy/lib/librcl_logging_interface.so", "role": "libraries"}, {"backtrace": 114, "fragment": "/opt/ros/jazzy/lib/librmw_implementation.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libament_index_cpp.so", "role": "libraries"}, {"backtrace": 114, "fragment": "/opt/ros/jazzy/lib/libtype_description_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 114, "fragment": "/opt/ros/jazzy/lib/libtype_description_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 114, "fragment": "/opt/ros/jazzy/lib/libtype_description_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 114, "fragment": "/opt/ros/jazzy/lib/libtype_description_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 114, "fragment": "/opt/ros/jazzy/lib/libtype_description_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 114, "fragment": "/opt/ros/jazzy/lib/libtype_description_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 114, "fragment": "/opt/ros/jazzy/lib/libtype_description_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 114, "fragment": "/opt/ros/jazzy/lib/libtype_description_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/librcl_yaml_param_parser.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/librosgraph_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/librosgraph_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/librosgraph_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/librosgraph_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/librosgraph_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/librosgraph_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/librosgraph_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/librosgraph_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libtracetools.so", "role": "libraries"}, {"backtrace": 121, "fragment": "-llttng-ust", "role": "libraries"}, {"backtrace": 121, "fragment": "-llttng-ust-common", "role": "libraries"}, {"backtrace": 121, "fragment": "-rdynamic", "role": "libraries"}, {"backtrace": 121, "fragment": "-ldl", "role": "libraries"}, {"backtrace": 136, "fragment": "/usr/lib/x86_64-linux-gnu/libconsole_bridge.so.1.0", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/librcl_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/librcl_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/librcl_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/librcl_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/librcl_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/librcl_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libcomposition_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/librcl_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libcomposition_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/librcl_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/librosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/librosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libfastcdr.so.2.2.5", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/librmw.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/librosidl_dynamic_typesupport.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/librosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/librosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libbuiltin_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/librosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libbuiltin_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libbuiltin_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/librosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/librcpputils.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/libservice_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 6, "fragment": "/opt/ros/jazzy/lib/libbuiltin_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/librosidl_runtime_c.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/jazzy/lib/librcutils.so", "role": "libraries"}, {"backtrace": 7, "fragment": "-ldl", "role": "libraries"}, {"fragment": "-Wl,-rpath-link,/opt/ros/jazzy/lib", "role": "libraries"}], "language": "CXX"}, "name": "list_devices_node", "nameOnDisk": "list_devices_node", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 2, "compileGroupIndex": 0, "path": "tools/list_devices_node.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}