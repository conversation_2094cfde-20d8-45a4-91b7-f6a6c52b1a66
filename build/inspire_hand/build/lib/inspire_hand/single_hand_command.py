import rclpy
from rclpy.node import Node
from std_msgs.msg import String, Int16MultiArray
from sensor_msgs.msg import JointState
import serial
import threading
import time

# 全局变量
right_hand_id = 0x01
left_hand_id = 0x01

# 把数据分成高字节和低字节
def data2bytes(data):
    rdata = [0xff] * 2
    if data == -1:
        rdata[0] = 0xff
        rdata[1] = 0xff
    else:
        rdata[0] = data & 0xff
        rdata[1] = (data >> 8) & (0xff)
    return rdata

# 把十六进制或十进制的数转成bytes
def num2str(num):
    str = hex(num)
    str = str[2:4]
    if (len(str) == 1):
        str = '0' + str
    str = bytes.fromhex(str)
    return str

# 求校验和
def checknum(data, leng):
    result = 0
    for i in range(2, leng):
        result += data[i]
    result = result & 0xff
    return result

# 设置角度
def setangle(ser, hand_id, lock, angle1, angle2, angle3, angle4, angle5, angle6):
    if angle1 < -1 or angle1 > 1000:
        print('数据超出正确范围：-1-1000')
        return
    if angle2 < -1 or angle2 > 1000:
        print('数据超出正确范围：-1-1000')
        return
    if angle3 < -1 or angle3 > 1000:
        print('数据超出正确范围：-1-1000')
        return
    if angle4 < -1 or angle4 > 1000:
        print('数据超出正确范围：-1-1000')
        return
    if angle5 < -1 or angle5 > 1000:
        print('数据超出正确范围：-1-1000')
        return
    if angle6 < -1 or angle6 > 1000:
        print('数据超出正确范围：-1-1000')
        return

    datanum = 0x0F
    b = [0] * (datanum + 5)
    # 包头
    b[0] = 0xEB
    b[1] = 0x90

    # hand_id号
    b[2] = hand_id

    # 数据个数
    b[3] = datanum

    # 写操作
    b[4] = 0x12

    # 地址
    b[5] = 0xCE
    b[6] = 0x05

    # 数据
    b[7] = data2bytes(angle1)[0]
    b[8] = data2bytes(angle1)[1]

    b[9] = data2bytes(angle2)[0]
    b[10] = data2bytes(angle2)[1]

    b[11] = data2bytes(angle3)[0]
    b[12] = data2bytes(angle3)[1]

    b[13] = data2bytes(angle4)[0]
    b[14] = data2bytes(angle4)[1]

    b[15] = data2bytes(angle5)[0]
    b[16] = data2bytes(angle5)[1]

    b[17] = data2bytes(angle6)[0]
    b[18] = data2bytes(angle6)[1]

    # 校验和
    b[19] = checknum(b, datanum + 4)

    # 向串口发送数据
    putdata = b''
    for i in range(1, datanum + 6):
        putdata = putdata + num2str(b[i - 1])
    # ser.write(putdata)
    # getdata = ser.read(9)
    try:
        with lock:
            ser.write(putdata)
            getdata = ser.read(9)
    except serial.SerialException as e:
        print(f"串口通信错误：{e}")

# 设置力控阈值
def setpower(ser, hand_id, lock, power1, power2, power3, power4, power5, power6):
    if power1 < 0 or power1 > 1000:
        print('数据超出正确范围：0-1000')
        return
    if power2 < 0 or power2 > 1000:
        print('数据超出正确范围：0-1000')
        return
    if power3 < 0 or power3 > 1000:
        print('数据超出正确范围：0-1000')
        return
    if power4 < 0 or power4 > 1000:
        print('数据超出正确范围：0-1000')
        return
    if power5 < 0 or power5 > 1000:
        print('数据超出正确范围：0-1000')
        return
    if power6 < 0 or power6 > 1000:
        print('数据超出正确范围：0-1000')
        return

    datanum = 0x0F
    b = [0] * (datanum + 5)
    # 包头
    b[0] = 0xEB
    b[1] = 0x90

    # hand_id号
    b[2] = hand_id

    # 数据个数
    b[3] = datanum

    # 写操作
    b[4] = 0x12

    # 地址
    b[5] = 0xDA
    b[6] = 0x05

    # 数据
    b[7] = data2bytes(power1)[0]
    b[8] = data2bytes(power1)[1]

    b[9] = data2bytes(power2)[0]
    b[10] = data2bytes(power2)[1]

    b[11] = data2bytes(power3)[0]
    b[12] = data2bytes(power3)[1]

    b[13] = data2bytes(power4)[0]
    b[14] = data2bytes(power4)[1]

    b[15] = data2bytes(power5)[0]
    b[16] = data2bytes(power5)[1]

    b[17] = data2bytes(power6)[0]
    b[18] = data2bytes(power6)[1]

    # 校验和
    b[19] = checknum(b, datanum + 4)

    # 向串口发送数据
    putdata = b''
    for i in range(1, datanum + 6):
        putdata = putdata + num2str(b[i - 1])
    # ser.write(putdata)
    # getdata = ser.read(9)
    try:
        with lock:  # 确保线程安全
            if not ser.is_open:
                if logger:
                    logger.error("串口未打开，尝试重新打开")
                else:
                    print("串口未打开，尝试重新打开")
                ser.open()
            ser.write(putdata)
            getdata = ser.read(9)
            if len(getdata) != 9:
                if logger:
                    logger.error(f"预期接收 9 字节，实际接收 {len(getdata)} 字节")
                else:
                    print(f"预期接收 9 字节，实际接收 {len(getdata)} 字节")
    except (serial.SerialException, TypeError) as e:
        if logger:
            logger.error(f"串口通信错误：{e}")
        else:
            print(f"串口通信错误：{e}")
    except Exception as e:
        if logger:
            logger.error(f"setpower 发生未知错误：{e}")
        else:
            print(f"setpower 发生未知错误：{e}")

# 设置速度
def setspeed(ser, hand_id, lock, speed1, speed2, speed3, speed4, speed5, speed6):
    if speed1 < 0 or speed1 > 1000:
        print('数据超出正确范围：0-1000')
        return
    if speed2 < 0 or speed2 > 1000:
        print('数据超出正确范围：0-1000')
        return
    if speed3 < 0 or speed3 > 1000:
        print('数据超出正确范围：0-1000')
        return
    if speed4 < 0 or speed4 > 1000:
        print('数据超出正确范围：0-1000')
        return
    if speed5 < 0 or speed5 > 1000:
        print('数据超出正确范围：0-1000')
        return
    if speed6 < 0 or speed6 > 1000:
        print('数据超出正确范围：0-1000')
        return

    datanum = 0x0F
    b = [0] * (datanum + 5)
    # 包头
    b[0] = 0xEB
    b[1] = 0x90

    # hand_id号
    b[2] = hand_id

    # 数据个数
    b[3] = datanum

    # 写操作
    b[4] = 0x12

    # 地址
    b[5] = 0xF2
    b[6] = 0x05

    # 数据
    b[7] = data2bytes(speed1)[0]
    b[8] = data2bytes(speed1)[1]

    b[9] = data2bytes(speed2)[0]
    b[10] = data2bytes(speed2)[1]

    b[11] = data2bytes(speed3)[0]
    b[12] = data2bytes(speed3)[1]

    b[13] = data2bytes(speed4)[0]
    b[14] = data2bytes(speed4)[1]

    b[15] = data2bytes(speed5)[0]
    b[16] = data2bytes(speed5)[1]

    b[17] = data2bytes(speed6)[0]
    b[18] = data2bytes(speed6)[1]

    # 校验和
    b[19] = checknum(b, datanum + 4)

    # 向串口发送数据
    putdata = b''
    for i in range(1, datanum + 6):
        putdata = putdata + num2str(b[i - 1])
    # ser.write(putdata)
    # getdata = ser.read(9)
    try:
        with lock:  # 确保线程安全
            if not ser.is_open:
                if logger:
                    logger.error("串口未打开，尝试重新打开")
                else:
                    print("串口未打开，尝试重新打开")
                ser.open()
            ser.write(putdata)
            getdata = ser.read(9)
            if len(getdata) != 9:
                if logger:
                    logger.error(f"预期接收 9 字节，实际接收 {len(getdata)} 字节")
                else:
                    print(f"预期接收 9 字节，实际接收 {len(getdata)} 字节")
    except (serial.SerialException, TypeError) as e:
        if logger:
            logger.error(f"串口通信错误：{e}")
        else:
            print(f"串口通信错误：{e}")
    except Exception as e:
        if logger:
            logger.error(f"setspeed 发生未知错误：{e}")
        else:
            print(f"setspeed 发生未知错误：{e}")

# 读取状态信息
def get_status(ser, hand_id):
    datanum = 0x04
    b = [0] * (datanum + 5)
    # 包头
    b[0] = 0xEB
    b[1] = 0x90

    # hand_id号
    b[2] = hand_id

    # 数据个数
    b[3] = datanum

    # 读操作
    b[4] = 0x11

    # 地址
    b[5] = 0x4C
    b[6] = 0x06

    # 读取寄存器的长度
    b[7] = 0x06

    # 校验和
    b[8] = checknum(b, datanum + 4)

    # 向串口发送数据
    putdata = b''
    for i in range(1, datanum + 6):
        putdata = putdata + num2str(b[i - 1])
    ser.write(putdata)
    getdata = ser.read(14)

    status = [0] * 6
    for i in range(1, 7):
        status[i - 1] = getdata[i + 6]
    return status

# 读取实际的角度值
def get_actangle(ser, hand_id, lock):
    datanum = 0x04
    b = [0] * (datanum + 5)
    # 包头
    b[0] = 0xEB
    b[1] = 0x90

    # hand_id号
    b[2] = hand_id

    # 数据个数
    b[3] = datanum

    # 读操作
    b[4] = 0x11

    # 地址
    b[5] = 0x0A
    b[6] = 0x06

    # 读取寄存器的长度
    b[7] = 0x0C

    # 校验和
    b[8] = checknum(b, datanum + 4)

    # 向串口发送数据
    putdata = b''
    for i in range(1, datanum + 6):
        putdata = putdata + num2str(b[i - 1])
    # ser.write(putdata)
    # getdata = ser.read(20)

    # actangle = [0] * 6
    # for i in range(1, 7):
    #     if getdata[i * 2 + 5] == 0xff and getdata[i * 2 + 6] == 0xff:
    #         actangle[i - 1] = -1
    #     else:
    #         actangle[i - 1] = getdata[i * 2 + 5] + (getdata[i * 2 + 6] << 8)
    # return actangle
    try:
        with lock:  # 确保线程安全
            ser.write(putdata)
            getdata = ser.read(20)

        if len(getdata) != 20:
            print(f"错误：预期接收 20 字节，实际接收 {len(getdata)} 字节")
            return None

        # 解析角度
        actangle = [0] * 6
        for i in range(6):
            low_byte = getdata[i * 2 + 7]
            high_byte = getdata[i * 2 + 8]
            if low_byte == 0xff and high_byte == 0xff:
                actangle[i] = -1
            else:
                actangle[i] = low_byte + (high_byte << 8)
        return actangle

    except serial.SerialException as e:
        print(f"串口通信错误：{e}")
        return None
    except Exception as e:
        print(f"get_actangle 发生未知错误：{e}")
        return None

# 读取手指状态并判断是否到位
def check_for_stop(ser, hand_id):
    status = get_status(ser, hand_id)
    '''
    # 判断是否力控到位停止
    if status[3] == 2:  # 假设状态 3 是力控到位停止
        #print("力控到位停止，读取实际角度并保持")
        
        actual_angle = get_actangle(ser, hand_id)
        setangle(ser, hand_id, actual_angle[0], actual_angle[1], actual_angle[2], actual_angle[3], actual_angle[4], actual_angle[5])
        print("实际角度已设置为: ", actual_angle)
    '''

# ROS2节点
class HandControlNode(Node):
    def __init__(self):
        super().__init__('hand_control_node')
        self.right_hand_command_sub = self.create_subscription(String,'/right_hand_command',self.right_hand_command_callback,10)
        self.left_hand_command_sub = self.create_subscription(String,'/left_hand_command',self.left_hand_command_callback,10)
        
        # 初始化左右手的串口通信
        self.ser_right = serial.Serial('/dev/ttyUSB1', 115200, timeout=1)
        self.ser_left = serial.Serial('/dev/ttyUSB2', 115200, timeout=1)

        # 设置初始角度和速度
        self.set_initial_angles_and_speeds()

        # 初始化力阈值
        self.right_power_threshold = None  # 右手的力阈值，默认为None
        self.left_power_threshold = None   # 左手的力阈值，默认为None
        self.finger_speed = 900
        # 定时器检查力控状态
        #self.timer = self.create_timer(0.5, self.timer_callback)  # 每秒检查一次手指状态

    def timer_callback(self):
        '''
        # 每秒检查一次状态
        check_for_stop(self.ser_right, right_hand_id)
        check_for_stop(self.ser_left, left_hand_id)
        '''

    def set_initial_angles_and_speeds(self):
        # 设置初始角度
        setangle(self.ser_right, right_hand_id, 1000, 1000, 1000, 1000, 1000, 50)  # 设置右手初始角度
        setangle(self.ser_left, left_hand_id, 1000, 1000, 1000, 1000, 1000, 50)    # 设置左手初始角度
        # 设置初始速度
        setspeed(self.ser_right, right_hand_id, self.finger_speed, self.finger_speed, self.finger_speed, self.finger_speed, self.finger_speed, 200)  # 设置右手初始速度
        setspeed(self.ser_left, left_hand_id, self.finger_speed, self.finger_speed, self.finger_speed, self.finger_speed, self.finger_speed, 200)    # 设置左手初始速度

    def right_hand_command_callback(self, msg):
        command = msg.data
        if command == 'grasp_high_power':
            self.get_logger().info('Received right hand grasp command')
            self.get_logger().info('Setting right hand power to high (300, 300, 300, 300, 300, 300)')
            self.right_power_threshold = (300, 300, 300, 300, 300, 300)  # 设置右手大力模式
            setangle(self.ser_right, right_hand_id, 100, 100, 100, 400, 700, 50)  # 设置右手角度
            setpower(self.ser_right, right_hand_id, *self.right_power_threshold)  # 使用当前设置的力阈值

        elif command == 'grasp_low_power':
            self.get_logger().info('Received right hand grasp command')
            self.get_logger().info('Setting right hand power to low (100, 100, 100, 100, 100, 100)')
            self.right_power_threshold = (100, 100, 100, 100, 100, 100)  # 设置右手小力模式
            setangle(self.ser_right, right_hand_id, 100, 100, 100, 400, 700, 50)  # 设置右手角度
            setpower(self.ser_right, right_hand_id, *self.right_power_threshold)  # 使用当前设置的力阈值

        elif command == 'release':
            self.get_logger().info('Received right hand release command')
            setangle(self.ser_right, right_hand_id, 1000, 1000, 1000, 1000, 1000, 50)  # 设置右手角度

        else:
            self.get_logger().info('Unknown right hand command: %s' % command)

    def left_hand_command_callback(self, msg):
        command = msg.data
        if command == 'grasp_high_power':
            self.get_logger().info('Received left hand grasp command')
            self.get_logger().info('Setting left hand power to high (300, 300, 300, 300, 300, 300)')
            self.left_power_threshold = (300, 300, 300, 300, 300, 300)  # 设置左手大力模式
            setangle(self.ser_left, left_hand_id, 100, 100, 100, 400, 700, 50)  # 设置左手角度
            setpower(self.ser_left, left_hand_id, *self.left_power_threshold)  # 使用当前设置的力阈值

        elif command == 'grasp_low_power':
            self.get_logger().info('Received left hand grasp command')
            self.get_logger().info('Setting left hand power to low (100, 100, 100, 100, 100, 100)')
            self.left_power_threshold = (100, 100, 100, 100, 100, 100)  # 设置左手小力模式
            setangle(self.ser_left, left_hand_id, 100, 100, 100, 400, 700, 50)  # 设置左手角度
            setpower(self.ser_left, left_hand_id, *self.left_power_threshold)  # 使用当前设置的力阈值

        elif command == 'release':
            self.get_logger().info('Received left hand release command')
            setangle(self.ser_left, left_hand_id, 1000, 1000, 1000, 1000, 1000, 50)  # 设置左手角度

        else:
            self.get_logger().info('Unknown left hand command: %s' % command)

class ComCtrlNode(Node):
    def __init__(self):
        super().__init__('hand_control_node')
        self.right_hand_command_sub = self.create_subscription(Int16MultiArray,'/right_hand_command',self.right_hand_command_callback,10)
        self.left_hand_command_sub = self.create_subscription(Int16MultiArray,'/left_hand_command',self.left_hand_command_callback,10)
        
        # 初始化左右手的串口通信
        self.ser_right = serial.Serial('/dev/ttyUSB1', 115200, timeout=1)
        self.ser_left = serial.Serial('/dev/ttyUSB2', 115200, timeout=1)
        self.right_lock = threading.Lock()  # 右手串口锁
        self.left_lock = threading.Lock()   # 左手串口锁

        self.finger_speed = 900
        # 设置初始角度和速度
        self.set_initial_angles_and_speeds()

        # 初始化力阈值
        self.right_power_threshold = None  # 右手的力阈值，默认为None
        self.left_power_threshold = None   # 左手的力阈值，默认为None

        # 定时器检查力控状态
        #self.timer = self.create_timer(0.5, self.timer_callback)  # 每秒检查一次手指状态

        # 创建手部关节状态发布者
        self.joint_state_pub = self.create_publisher(JointState, '/hand_joints_state', 10)
        # 关节名称
        self.joint_names = [
            'left_thumb_horizontal', 'left_thumb_vertical', 'left_index_vertical',
            'left_middle_vertical', 'left_ring_vertical', 'left_pinky_vertical',
            'right_thumb_horizontal', 'right_thumb_vertical', 'right_index_vertical',
            'right_middle_vertical', 'right_ring_vertical', 'right_pinky_vertical'
        ]
        # 启动发布关节状态的线程
        self.joint_state_thread = threading.Thread(target=self.publish_joint_states, daemon=True)
        self.joint_state_thread.start()

    def publish_joint_states(self):
        rate = 60.0  # 60Hz
        period = 1.0 / rate  # 每帧时间间隔（秒）
        
        while rclpy.ok():
            start_time = time.time()

            # 获取左右手的实际角度
            right_angles = get_actangle(self.ser_right, right_hand_id, self.right_lock)
            left_angles = get_actangle(self.ser_left, left_hand_id, self.left_lock)

            # 检查错误
            if right_angles is None or left_angles is None:
                self.get_logger().error("无法获取关节角度")
                continue

            # 创建 JointState 消息
            joint_state = JointState()
            joint_state.header.stamp = self.get_clock().now().to_msg()
            joint_state.name = self.joint_names
            joint_state.position = [
                float(left_angles[0]) / 1000.0,  # 左手大拇指横向
                float(left_angles[1]) / 1000.0,  # 左手大拇指纵向
                float(left_angles[2]) / 1000.0,  # 左手食指纵向
                float(left_angles[3]) / 1000.0,  # 左手中指纵向
                float(left_angles[4]) / 1000.0,  # 左手无名指纵向
                float(left_angles[5]) / 1000.0,  # 左手小指纵向
                float(right_angles[0]) / 1000.0,  # 右手大拇指横向
                float(right_angles[1]) / 1000.0,  # 右手大拇指纵向
                float(right_angles[2]) / 1000.0,  # 右手食指纵向
                float(right_angles[3]) / 1000.0,  # 右手中指纵向
                float(right_angles[4]) / 1000.0,  # 右手无名指纵向
                float(right_angles[5]) / 1000.0   # 右手小指纵向
            ]

            # 发布关节状态
            self.joint_state_pub.publish(joint_state)

            # 控制发布频率
            elapsed = time.time() - start_time
            sleep_time = max(0.0, period - elapsed)
            time.sleep(sleep_time)


    def timer_callback(self):
        '''
        # 每秒检查一次状态
        check_for_stop(self.ser_right, right_hand_id)
        check_for_stop(self.ser_left, left_hand_id)
        '''

    def set_initial_angles_and_speeds(self):
        # 设置初始角度
        setangle(self.ser_right, right_hand_id, self.right_lock, 1000, 1000, 1000, 1000, 1000, 50)  # 设置右手初始角度
        setangle(self.ser_left, left_hand_id, self.left_lock, 1000, 1000, 1000, 1000, 1000, 50)    # 设置左手初始角度
        # 设置初始速度
        setspeed(self.ser_right, right_hand_id, self.right_lock, self.finger_speed, self.finger_speed, self.finger_speed, self.finger_speed, self.finger_speed, self.finger_speed)  # 设置右手初始速度
        setspeed(self.ser_left, left_hand_id, self.left_lock, self.finger_speed, self.finger_speed, self.finger_speed, self.finger_speed, self.finger_speed, self.finger_speed)    # 设置左手初始速度

    def right_hand_command_callback(self, msg):
        command = msg.data
        
        # self.get_logger().info('Received right hand grasp command')
        # self.get_logger().info('Setting right hand power to low (100, 100, 100, 100, 100, 100)')
        self.right_power_threshold = (100, 100, 100, 100, 100, 100)  # 设置右手小力模式
        setangle(self.ser_right, right_hand_id, self.right_lock, *command)  # 设置右手角度
        setpower(self.ser_right, right_hand_id, self.right_lock, *self.right_power_threshold)  # 使用当前设置的力阈值

    def left_hand_command_callback(self, msg):
        command = msg.data
        
        # self.get_logger().info('Received left hand grasp command')
        # self.get_logger().info('Setting left hand power to low (100, 100, 100, 100, 100, 100)')
        self.left_power_threshold = (100, 100, 100, 100, 100, 100)  # 设置左手小力模式
        setangle(self.ser_left, left_hand_id, self.left_lock, *command)  # 设置左手角度
        setpower(self.ser_left, left_hand_id, self.left_lock, *self.left_power_threshold)  # 使用当前设置的力阈值


# def main(args=None):
#     rclpy.init(args=args)
#     node = ComCtrlNode()
#     rclpy.spin(node)
#     node.destroy_node()
#     rclpy.shutdown()

def main(args=None):
    rclpy.init(args=args)
    node = ComCtrlNode()
    try:
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        node.ser_right.close()
        node.ser_left.close()
        node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
