import rclpy
from rclpy.node import Node
from std_msgs.msg import String, Int32MultiArray
import serial

# 全局变量
right_hand_id = 0x01
left_hand_id = 0x01

# 把数据分成高字节和低字节
def data2bytes(data):
    rdata = [0xff] * 2
    if data == -1:
        rdata[0] = 0xff
        rdata[1] = 0xff
    else:
        rdata[0] = data & 0xff
        rdata[1] = (data >> 8) & (0xff)
    return rdata

# 把十六进制或十进制的数转成bytes
def num2str(num):
    str = hex(num)
    str = str[2:4]
    if (len(str) == 1):
        str = '0' + str
    str = bytes.fromhex(str)
    return str

# 求校验和
def checknum(data, leng):
    result = 0
    for i in range(2, leng):
        result += data[i]
    result = result & 0xff
    return result

# 设置角度
def setangle(ser, hand_id, angle1, angle2, angle3, angle4, angle5, angle6):
    if angle1 < -1 or angle1 > 1000:
        print('数据超出正确范围：-1-1000')
        return
    if angle2 < -1 or angle2 > 1000:
        print('数据超出正确范围：-1-1000')
        return
    if angle3 < -1 or angle3 > 1000:
        print('数据超出正确范围：-1-1000')
        return
    if angle4 < -1 or angle4 > 1000:
        print('数据超出正确范围：-1-1000')
        return
    if angle5 < -1 or angle5 > 1000:
        print('数据超出正确范围：-1-1000')
        return
    if angle6 < -1 or angle6 > 1000:
        print('数据超出正确范围：-1-1000')
        return

    datanum = 0x0F
    b = [0] * (datanum + 5)
    # 包头
    b[0] = 0xEB
    b[1] = 0x90

    # hand_id号
    b[2] = hand_id

    # 数据个数
    b[3] = datanum

    # 写操作
    b[4] = 0x12

    # 地址
    b[5] = 0xCE
    b[6] = 0x05

    # 数据
    b[7] = data2bytes(angle1)[0]
    b[8] = data2bytes(angle1)[1]

    b[9] = data2bytes(angle2)[0]
    b[10] = data2bytes(angle2)[1]

    b[11] = data2bytes(angle3)[0]
    b[12] = data2bytes(angle3)[1]

    b[13] = data2bytes(angle4)[0]
    b[14] = data2bytes(angle4)[1]

    b[15] = data2bytes(angle5)[0]
    b[16] = data2bytes(angle5)[1]

    b[17] = data2bytes(angle6)[0]
    b[18] = data2bytes(angle6)[1]

    # 校验和
    b[19] = checknum(b, datanum + 4)

    # 向串口发送数据
    putdata = b''
    for i in range(1, datanum + 6):
        putdata = putdata + num2str(b[i - 1])
    ser.write(putdata)
    getdata = ser.read(9)

# 设置力控阈值
def setpower(ser, hand_id, power1, power2, power3, power4, power5, power6):
    if power1 < 0 or power1 > 1000:
        print('数据超出正确范围：0-1000')
        return
    if power2 < 0 or power2 > 1000:
        print('数据超出正确范围：0-1000')
        return
    if power3 < 0 or power3 > 1000:
        print('数据超出正确范围：0-1000')
        return
    if power4 < 0 or power4 > 1000:
        print('数据超出正确范围：0-1000')
        return
    if power5 < 0 or power5 > 1000:
        print('数据超出正确范围：0-1000')
        return
    if power6 < 0 or power6 > 1000:
        print('数据超出正确范围：0-1000')
        return

    datanum = 0x0F
    b = [0] * (datanum + 5)
    # 包头
    b[0] = 0xEB
    b[1] = 0x90

    # hand_id号
    b[2] = hand_id

    # 数据个数
    b[3] = datanum

    # 写操作
    b[4] = 0x12

    # 地址
    b[5] = 0xDA
    b[6] = 0x05

    # 数据
    b[7] = data2bytes(power1)[0]
    b[8] = data2bytes(power1)[1]

    b[9] = data2bytes(power2)[0]
    b[10] = data2bytes(power2)[1]

    b[11] = data2bytes(power3)[0]
    b[12] = data2bytes(power3)[1]

    b[13] = data2bytes(power4)[0]
    b[14] = data2bytes(power4)[1]

    b[15] = data2bytes(power5)[0]
    b[16] = data2bytes(power5)[1]

    b[17] = data2bytes(power6)[0]
    b[18] = data2bytes(power6)[1]

    # 校验和
    b[19] = checknum(b, datanum + 4)

    # 向串口发送数据
    putdata = b''
    for i in range(1, datanum + 6):
        putdata = putdata + num2str(b[i - 1])
    ser.write(putdata)
    getdata = ser.read(9)

# 设置速度
def setspeed(ser, hand_id, speed1, speed2, speed3, speed4, speed5, speed6):
    if speed1 < 0 or speed1 > 1000:
        print('数据超出正确范围：0-1000')
        return
    if speed2 < 0 or speed2 > 1000:
        print('数据超出正确范围：0-1000')
        return
    if speed3 < 0 or speed3 > 1000:
        print('数据超出正确范围：0-1000')
        return
    if speed4 < 0 or speed4 > 1000:
        print('数据超出正确范围：0-1000')
        return
    if speed5 < 0 or speed5 > 1000:
        print('数据超出正确范围：0-1000')
        return
    if speed6 < 0 or speed6 > 1000:
        print('数据超出正确范围：0-1000')
        return

    datanum = 0x0F
    b = [0] * (datanum + 5)
    # 包头
    b[0] = 0xEB
    b[1] = 0x90

    # hand_id号
    b[2] = hand_id

    # 数据个数
    b[3] = datanum

    # 写操作
    b[4] = 0x12

    # 地址
    b[5] = 0xF2
    b[6] = 0x05

    # 数据
    b[7] = data2bytes(speed1)[0]
    b[8] = data2bytes(speed1)[1]

    b[9] = data2bytes(speed2)[0]
    b[10] = data2bytes(speed2)[1]

    b[11] = data2bytes(speed3)[0]
    b[12] = data2bytes(speed3)[1]

    b[13] = data2bytes(speed4)[0]
    b[14] = data2bytes(speed4)[1]

    b[15] = data2bytes(speed5)[0]
    b[16] = data2bytes(speed5)[1]

    b[17] = data2bytes(speed6)[0]
    b[18] = data2bytes(speed6)[1]

    # 校验和
    b[19] = checknum(b, datanum + 4)

    # 向串口发送数据
    putdata = b''
    for i in range(1, datanum + 6):
        putdata = putdata + num2str(b[i - 1])
    ser.write(putdata)
    getdata = ser.read(9)

 #读取实际的受力
def get_actforce(ser, hand_id):

    datanum = 0x04
    b = [0]*(datanum + 5)
    #包头
    b[0] = 0xEB
    b[1] = 0x90

    #hand_id号
    b[2] = hand_id

    #数据个数
    b[3] = datanum
    
    #读操作
    b[4] = 0x11
    
    #地址
    b[5] = 0x2E
    b[6] = 0x06
     
    #读取寄存器的长度
    b[7] = 0x0C
    
    #校验和
    b[8] = checknum(b,datanum+4)
    
    #向串口发送数据
    putdata = b''
    
    for i in range(1,datanum+6):
        putdata = putdata + num2str(b[i-1])
    ser.write(putdata)
    #print('发送的数据：')
    #for i in range(1,datanum+6):
    #    print(hex(putdata[i-1]))
    
    getdata= ser.read(20)
    #print('返回的数据：')
    #for i in range(1,21):
    #    print(hex(getdata[i-1]))
    
    actforce = [0]*6
    for i in range(1,7):
        if getdata[i*2+5]== 0xff and getdata[i*2+6]== 0xff:
            actforce[i-1] = -1
        else:
            actforce[i-1] = int.from_bytes(bytes([getdata[i*2+5] , getdata[i*2+6]]) , byteorder='little',signed=True)
    return actforce

# 读取实际的角度值
def get_actangle(ser, hand_id):
    datanum = 0x04
    b = [0] * (datanum + 5)
    # 包头
    b[0] = 0xEB
    b[1] = 0x90

    # hand_id号
    b[2] = hand_id

    # 数据个数
    b[3] = datanum

    # 读操作
    b[4] = 0x11

    # 地址
    b[5] = 0x0A
    b[6] = 0x06

    # 读取寄存器的长度
    b[7] = 0x0C

    # 校验和
    b[8] = checknum(b, datanum + 4)

    # 向串口发送数据
    putdata = b''
    for i in range(1, datanum + 6):
        putdata = putdata + num2str(b[i - 1])
    ser.write(putdata)
    getdata = ser.read(20)

    actangle = [0] * 6
    for i in range(1, 7):
        if getdata[i * 2 + 5] == 0xff and getdata[i * 2 + 6] == 0xff:
            actangle[i - 1] = -1
        else:
            actangle[i - 1] = getdata[i * 2 + 5] + (getdata[i * 2 + 6] << 8)
    return actangle

# 读取手指状态并判断是否到位
def check_for_stop(ser, hand_id):
    status = get_status(ser, hand_id)
    '''
    # 判断是否力控到位停止
    if status[3] == 2:  # 假设状态 3 是力控到位停止
        #print("力控到位停止，读取实际角度并保持")
        
        actual_angle = get_actangle(ser, hand_id)
        setangle(ser, hand_id, actual_angle[0], actual_angle[1], actual_angle[2], actual_angle[3], actual_angle[4], actual_angle[5])
        print("实际角度已设置为: ", actual_angle)
    '''

# ROS2节点
class HandControlNode(Node):
    def __init__(self):
        super().__init__('hand_control_node')
        self.right_hand_command_sub = self.create_subscription(String,'/right_hand_command',self.right_hand_command_callback,10)
        self.left_hand_command_sub = self.create_subscription(String,'/left_hand_command',self.left_hand_command_callback,10)
        
        # 灵巧手状态发布
        self.left_position_pub = self.create_publisher(
            Int32MultiArray, '/left_gripper_position', 10)
        self.left_force_pub = self.create_publisher(
            Int32MultiArray, '/left_gripper_force', 10)
        self.right_position_pub = self.create_publisher(
            Int32MultiArray, '/right_gripper_position', 10)
        self.right_force_pub = self.create_publisher(
            Int32MultiArray, '/right_gripper_force', 10)
        
        # 初始化左右手的串口通信
        self.ser_right = serial.Serial('/dev/ttyUSB1', 115200, timeout=1)
        self.ser_left = serial.Serial('/dev/ttyUSB0', 115200, timeout=1)

        # 设置初始角度和速度
        self.set_initial_angles_and_speeds()

        # 初始化力阈值
        self.right_power_threshold = None  # 右手的力阈值，默认为None
        self.left_power_threshold = None   # 左手的力阈值，默认为None

        # 初始化双手状态
        self.hand_status = {"left": "releasing", "right": "releasing"}

        # 定时器发布状态
        self.timer = self.create_timer(0.05, self.timer_callback)  # 每秒检查一次手指状态

    def timer_callback(self):
        # 获取当前灵巧手状态 并发布
        actual_angle_right = get_actangle(self.ser_right, right_hand_id)
        actual_force_right = get_actforce(self.ser_right, right_hand_id)

        actual_angle_left = get_actangle(self.ser_left, left_hand_id)
        actual_force_left = get_actforce(self.ser_left, left_hand_id)
        '''
        print("actual_angle_right\t",actual_angle_right)
        print("actual_force_right\t",actual_force_right)
        print("actual_angle_left\t",actual_angle_left)
        print("actual_force_left\t",actual_force_left)
        '''
        # 发布数据
        def create_int32_array(data):
            msg = Int32MultiArray()
            msg.data = [int(x) for x in data]
            return msg

        self.left_position_pub.publish(create_int32_array(actual_angle_left))
        self.left_force_pub.publish(create_int32_array(actual_force_left))
        self.right_position_pub.publish(create_int32_array(actual_angle_right))
        self.right_force_pub.publish(create_int32_array(actual_force_right))


    def set_initial_angles_and_speeds(self):
        # 设置初始角度
        setangle(self.ser_right, right_hand_id, 1000, 1000, 1000, 1000, 1000, 50)  # 设置右手初始角度
        setangle(self.ser_left, left_hand_id, 1000, 1000, 1000, 1000, 1000, 50)    # 设置左手初始角度
        # 设置初始速度
        setspeed(self.ser_right, right_hand_id, 100, 100, 100, 100, 100, 100)  # 设置右手初始速度
        setspeed(self.ser_left, left_hand_id, 100, 100, 100, 100, 100, 100)    # 设置左手初始速度

    def right_hand_command_callback(self, msg):
        command = msg.data
        hand = "right"
        # 卓能 task1 扫码 
        # btn1 对应 食指突出 放置复位
        # btn2 对应 抓取 放置

        self.right_power_threshold = (300, 300, 300, 300, 300, 250)  # 设置右手大力模式
        
        setpower(self.ser_right, right_hand_id, *self.right_power_threshold)  # 使用当前设置的力阈值

        angle_target = (0,0,0,0,0,0)

        
        if command == "btn1":
            if self.hand_status[hand] == "releasing":
                angle_target = (100, 100, 100, 1000, 883, 138)
                self.hand_status[hand] = "press"


            elif self.hand_status[hand] == "press"  :
                angle_target = (1000, 1000, 1000, 1000, 1000, 138)
                self.hand_status[hand] = "releasing"
            else:
                return

        elif command == "btn2":
            if self.hand_status[hand] == "releasing" :
                self.right_power_threshold = (700, 700, 700, 700, 700, 250)  # 设置右手大力模式
                angle_target = (100, 100, 100, 400, 700, 50)
                self.hand_status[hand] = "grasp"

            elif self.hand_status[hand] == "grasp" :
                angle_target = (1000, 1000, 1000, 1000, 1000, 138)
                self.hand_status[hand] = "releasing"

            else :
                return

        self.get_logger().info(f'Received right hand grasp command {self.hand_status[hand]}')
        self.get_logger().info(f'Setting right hand power to high {angle_target}')
        setangle(self.ser_right, right_hand_id, *angle_target )  # 设置右手角度




    def left_hand_command_callback(self, msg):
        command = msg.data
        hand = "left"
        # 卓能 task2 抓取 按按钮 
        # btn1 对应 食指突出 放置复位
        # btn2 对应 抓取 放置

        self.left_power_threshold = (300, 300, 300, 300, 300, 250)  # 设置右手大力模式
        
        

        angle_target = (0,0,0,0,0,0)

        
        if command == "btn1":
            if self.hand_status[hand] == "releasing":
                angle_target = (100, 100, 100, 1000, 883, 138)
                self.hand_status[hand] = "press"


            elif self.hand_status[hand] == "press"  :
                angle_target = (1000, 1000, 1000, 1000, 1000, 138)
                self.hand_status[hand] = "releasing"
            else:
                return

        elif command == "btn2":
            if self.hand_status[hand] == "releasing" :
                self.left_power_threshold = (700, 700, 700, 700, 700, 250)  # 设置右手大力模式
                angle_target = (100, 100, 100, 400, 700, 50)
                self.hand_status[hand] = "grasp"

            elif self.hand_status[hand] == "grasp" :
                angle_target = (1000, 1000, 1000, 1000, 1000, 138)
                self.hand_status[hand] = "releasing"

            else :
                return

        setpower(self.ser_left, left_hand_id, *self.left_power_threshold)  # 使用当前设置的力阈值
        self.get_logger().info(f'Received left hand grasp command {self.hand_status[hand]}')
        self.get_logger().info(f'Setting left hand power to high {angle_target}')
        setangle(self.ser_left, left_hand_id, *angle_target )  # 设置右手角度



def main(args=None):
    rclpy.init(args=args)
    node = HandControlNode()
    rclpy.spin(node)
    node.destroy_node()
    rclpy.shutdown()

if __name__ == '__main__':
    main()
