# 夹爪控制系统测试指南

## 概述

本测试系统为夹爪控制器提供了完整的单元测试、集成测试和性能测试功能，使用 Python 的 unittest 框架，并集成了完善的日志系统。

## 文件结构

```
├── main.py                      # 演示程序
├── test_gripper.py             # 单元测试（模拟测试）
├── test_gripper_integration.py # 集成测试（需要硬件）
├── run_tests.py                # 测试运行器
├── docs/
│   └── testing-guide.md        # 本文档
└── logs/                       # 自动生成的日志文件
```

## 主要特性

### 1. 统一夹爪控制类 (GripperController)

- **支持多种夹爪类型**: CAN 和 RS485 夹爪的统一接口
- **异步状态回调**: 实时接收夹爪状态更新
- **状态日志记录**: 自动记录所有状态变化
- **便捷控制方法**: 提供开合、位置控制等高级接口

### 2. glog 风格日志系统

- **多级别日志**: DEBUG, INFO, WARNING, ERROR
- **文件和控制台输出**: 同时输出到日志文件和控制台
- **时间戳记录**: 精确记录每个操作的时间
- **自动日志文件**: 按时间戳自动生成日志文件

### 3. 完整测试套件

- **单元测试**: 使用 mock 对象测试所有功能
- **集成测试**: 测试实际硬件连接和性能
- **性能测试**: 响应时间、并发操作等性能指标
- **多夹爪测试**: 测试多个夹爪的协调操作

## 快速开始

### 1. 运行所有测试

```bash
python3 run_tests.py
```

### 2. 运行特定测试

```bash
# 只运行单元测试（不需要硬件）
python3 run_tests.py unit

# 只运行集成测试（需要硬件）
python3 run_tests.py integration

# 运行演示程序
python3 run_tests.py demo
```

### 3. 直接运行测试文件

```bash
# 单元测试
python3 test_gripper.py

# 集成测试
python3 test_gripper_integration.py

# 演示程序
python3 main.py
```

## 使用示例

### 基本使用

```python
from gripper_controller import create_can_gripper, GripperType
from logger_config import get_test_logger

# 获取测试日志
logger = get_test_logger()

# 创建CAN夹爪控制器
can_gripper = create_can_gripper(node_id=1, channel=0)

# 设置状态回调
def status_callback(status):
    logger.info(f"夹爪状态: {status}")

can_gripper.set_status_callback(status_callback)

# 控制夹爪
can_gripper.open_gripper()           # 打开夹爪
can_gripper.close_gripper()          # 闭合夹爪
can_gripper.move_to_position(0x7F)   # 移动到中间位置

# 获取状态
last_status = can_gripper.get_last_status()

# 关闭
can_gripper.close()
```

### 多夹爪协调

```python
from gripper_controller import MultiGripperController, GripperType

# 创建多夹爪控制器
multi = MultiGripperController()

# 添加夹爪
multi.add_gripper("gripper1", GripperType.CAN, node_id=1, channel=0)
multi.add_gripper("gripper2", GripperType.RS485, port='/dev/ttyUSB0', node_id=1)

# 同步位置控制
positions = {"gripper1": 0x80, "gripper2": 0x80}
multi.synchronize_positions(positions, timeout=5.0)

# 关闭所有夹爪
multi.close_all()
```

## 测试类型说明

### 单元测试 (test_gripper.py)

- **TestSingleGripperCAN**: CAN 夹爪基础功能测试
- **TestSingleGripperRS485**: RS485 夹爪基础功能测试
- **TestMultipleGrippers**: 多夹爪测试
- **TestGripperControllerFeatures**: 控制器功能测试

特点：

- 使用 mock 对象，不需要实际硬件
- 测试所有 API 接口和功能
- 快速执行，适合开发阶段

### 集成测试 (test_gripper_integration.py)

- **TestSingleGripperSequence**: 单夹爪完整动作序列
- **TestMultiGripperCoordination**: 多夹爪协调测试
- **TestGripperPerformance**: 性能和响应时间测试

特点：

- 需要实际硬件连接
- 测试真实的硬件交互
- 包含性能基准测试

## 日志系统

### 日志级别

- **DEBUG**: 详细的调试信息（状态回调、指令详情等）
- **INFO**: 一般信息（操作开始/完成、状态变化等）
- **WARNING**: 警告信息（可选测试失败、硬件未连接等）
- **ERROR**: 错误信息（必需测试失败、异常等）

### 日志文件

- 自动生成格式: `gripper_YYYYMMDD_HHMMSS.log`
- 位置: 当前工作目录
- 编码: UTF-8
- 包含时间戳、级别、文件名、行号等信息

### 日志配置

日志系统在首次使用时自动初始化，无需手动配置。如需自定义，可修改`main.py`中的`GripperLogger`类。

## 故障排除

### 常见问题

1. **硬件连接失败**

   - 检查 CAN 设备权限和驱动
   - 确认 RS485 串口设备存在
   - 查看日志文件中的详细错误信息

2. **测试失败**

   - 单元测试失败: 检查代码逻辑错误
   - 集成测试失败: 通常是硬件连接问题，可以跳过

3. **权限问题**
   - CAN 设备可能需要 root 权限
   - 串口设备需要适当的用户组权限

### 调试技巧

1. **查看详细日志**

   ```bash
   tail -f gripper_*.log
   ```

2. **运行单个测试方法**

   ```bash
   python3 -m unittest test_gripper.TestSingleGripperCAN.test_can_gripper_initialization
   ```

3. **增加日志级别**
   修改`main.py`中的日志级别为 DEBUG 以获取更多信息

## 扩展开发

### 添加新测试

1. 在相应的测试文件中添加新的测试方法
2. 方法名以`test_`开头
3. 使用`self.assert*`方法进行断言
4. 添加适当的日志记录

### 添加新功能

1. 在`GripperController`类中添加新方法
2. 添加相应的日志记录
3. 编写对应的单元测试
4. 更新文档

## 性能基准

在标准测试环境下的性能指标：

- **指令响应时间**: < 50ms (平均)
- **状态回调频率**: > 10Hz
- **并发操作**: 支持 3 个以上夹爪同时操作
- **测试覆盖率**: > 90%

## 依赖要求

- Python 3.6+
- pyserial (用于 RS485)
- libcontrolcan.so (用于 CAN)
- unittest (Python 标准库)
- threading (Python 标准库)
- logging (Python 标准库)

## 许可证

与主项目相同的许可证。
