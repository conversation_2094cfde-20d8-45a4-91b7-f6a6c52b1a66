#!/usr/bin/env python3
"""
使用线程池管理组件的RS485夹爪使用示例

这个示例展示了如何使用集成了线程池管理组件的RS485夹爪类，包括：
- 创建和连接夹爪
- 监控性能和状态（100Hz频率）
- 处理连接管理
- 获取统计信息

作者: Augment Agent
版本: 1.0.0
"""

import sys
import os
import time
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.Gripper_485_with_threadpool import GripperRS485WithThreadPool
from common.logger_config import get_logger

# 配置主程序日志
logger = get_logger(
    name='gripper_485_threadpool_example',
    log_dir=os.path.join(os.getcwd(), "logs/examples"),
    log_level=logging.DEBUG,
    console_level=logging.INFO,
    file_level=logging.DEBUG
)


def demonstrate_basic_usage():
    """演示基本使用方法"""
    logger.info("=== RS485夹爪基本使用演示 ===")
    
    # 创建夹爪实例（注意：这里使用虚拟串口，实际使用时请替换为真实串口）
    gripper = GripperRS485WithThreadPool(port='/dev/ttyUSB0', node_id=1, baudrate=115200)
    
    try:
        # 启动连接管理
        gripper.connection_manager.start_monitoring()
        
        # 连接夹爪
        if gripper.connection_manager.connect():
            logger.info("夹爪连接成功")
        else:
            logger.warning("夹爪连接失败，将自动重试")
        
        # 运行一段时间，观察系统行为
        logger.info("系统运行中，观察性能和状态...")
        
        for i in range(15):
            time.sleep(1)
            
            # 获取当前状态
            status = gripper.get_last_status()
            if status:
                logger.info(f"当前状态: 位置=0x{status.get('position', 0):02X}, "
                          f"接收次数={status.get('receive_count', 0)}, "
                          f"故障={status.get('fault_description', 'N/A')}")
            
            # 每5秒输出一次性能报告
            if i % 5 == 4:
                show_performance_report(gripper)
        
    except Exception as e:
        logger.error(f"演示过程中发生异常: {e}")
    finally:
        # 清理资源
        gripper.close()
        logger.info("夹爪已关闭")


def demonstrate_high_frequency_monitoring():
    """演示100Hz高频监控功能"""
    logger.info("=== 100Hz高频监控演示 ===")
    
    gripper = GripperRS485WithThreadPool(port='/dev/ttyUSB0', node_id=2, baudrate=115200)
    
    # 设置状态变化回调
    callback_count = 0
    def on_status_change(status_dict):
        nonlocal callback_count
        callback_count += 1
        if callback_count % 100 == 0:  # 每100次回调输出一次
            logger.info(f"状态回调 #{callback_count}: 位置=0x{status_dict.get('position', 0):02X}")
    
    gripper.set_status_callback(on_status_change)
    
    try:
        # 启动系统
        gripper.connection_manager.start_monitoring()
        gripper.connection_manager.connect()
        
        # 确保100Hz频率
        gripper.set_query_interval(0.01)  # 10ms = 100Hz
        
        logger.info("开始100Hz高频监控...")
        
        # 运行10秒收集数据
        for i in range(10):
            time.sleep(1)
            
            # 获取性能指标
            metrics = gripper.get_performance_metrics()
            logger.info(f"第{i+1}秒: 频率={metrics['callback_frequency_hz']:.1f}Hz, "
                       f"响应时间={metrics['avg_response_time_ms']:.1f}ms")
        
        # 最终性能报告
        final_metrics = gripper.get_performance_metrics()
        logger.info(f"最终性能: 频率={final_metrics['callback_frequency_hz']:.1f}Hz, "
                   f"稳定性={final_metrics['frequency_stability']:.2f}, "
                   f"等级={final_metrics['overall_performance']}")
        
        # 获取优化建议
        suggestions = gripper.performance_monitor.get_optimization_suggestions()
        if suggestions:
            logger.info("性能优化建议:")
            for suggestion in suggestions:
                logger.info(f"  - {suggestion}")
        else:
            logger.info("当前性能良好，无需优化")
        
    finally:
        gripper.close()


def demonstrate_gripper_operations():
    """演示夹爪操作功能"""
    logger.info("=== 夹爪操作演示 ===")
    
    gripper = GripperRS485WithThreadPool(port='/dev/ttyUSB0', node_id=3, baudrate=115200)
    
    try:
        gripper.connection_manager.start_monitoring()
        gripper.connection_manager.connect()
        
        # 等待连接稳定
        time.sleep(2)
        
        logger.info("开始夹爪操作序列...")
        
        # 操作序列
        operations = [
            ("打开夹爪", lambda: gripper.open_gripper(force=0x80)),
            ("移动到中间位置", lambda: gripper.move_to_position(0x80, force=0x60)),
            ("关闭夹爪", lambda: gripper.close_gripper(force=0x40)),
            ("移动到3/4位置", lambda: gripper.move_to_position(0xC0, force=0x80))
        ]
        
        for operation_name, operation_func in operations:
            logger.info(f"执行: {operation_name}")
            
            # 记录操作前的状态
            before_status = gripper.get_last_status()
            if before_status:
                logger.info(f"操作前位置: 0x{before_status.get('position', 0):02X}")
            
            # 执行操作
            result = operation_func()
            
            if result:
                logger.info(f"操作完成: 位置=0x{result.get('position', 0):02X}, "
                          f"状态={result.get('state_description', 'N/A')}")
            else:
                logger.warning(f"操作失败: {operation_name}")
            
            # 等待一段时间
            time.sleep(2)
        
        logger.info("夹爪操作序列完成")
        
    finally:
        gripper.close()


def demonstrate_connection_management():
    """演示连接管理功能"""
    logger.info("=== 连接管理演示 ===")
    
    gripper = GripperRS485WithThreadPool(port='/dev/ttyUSB0', node_id=4, baudrate=115200)
    
    # 设置连接事件回调
    connection_events = []
    
    def on_connected():
        connection_events.append("connected")
        logger.info("连接事件: 已连接")
    
    def on_disconnected():
        connection_events.append("disconnected")
        logger.info("连接事件: 已断开")
    
    def on_reconnect_attempt(count):
        connection_events.append(f"reconnect_attempt_{count}")
        logger.info(f"连接事件: 重连尝试 (第{count}次)")
    
    gripper.connection_manager.add_callback('on_connected', on_connected)
    gripper.connection_manager.add_callback('on_disconnected', on_disconnected)
    gripper.connection_manager.add_callback('on_reconnect_attempt', on_reconnect_attempt)
    
    try:
        # 启动连接管理
        gripper.connection_manager.start_monitoring()
        
        # 初始连接
        success = gripper.connection_manager.connect()
        logger.info(f"初始连接: {'成功' if success else '失败'}")
        
        time.sleep(3)
        
        # 模拟连接丢失
        logger.info("模拟连接丢失...")
        gripper.connection_manager.disconnect()
        
        # 等待自动重连
        logger.info("等待自动重连...")
        time.sleep(8)
        
        # 获取连接统计
        stats = gripper.connection_manager.get_statistics()
        logger.info(f"连接统计: 总连接={stats.total_connections}, "
                   f"成功率={stats.success_rate:.1%}, "
                   f"重连次数={stats.reconnect_attempts}")
        
        logger.info(f"连接事件记录: {connection_events}")
        
    finally:
        gripper.close()


def show_performance_report(gripper):
    """显示性能报告"""
    logger.info("--- 性能报告 ---")
    
    # 获取系统统计
    stats = gripper.get_system_statistics()
    
    # 线程池统计
    thread_stats = stats['thread_pool_stats']
    logger.info(f"线程池: 总任务={thread_stats['total_tasks']}, "
               f"成功率={thread_stats['success_rate']:.1%}, "
               f"活跃任务={thread_stats['active_tasks']}")
    
    # 状态管理器统计
    status_stats = stats['status_manager_stats']
    logger.info(f"状态管理: 更新={status_stats['total_updates']}, "
               f"读取={status_stats['total_reads']}, "
               f"版本={status_stats['current_version']}")
    
    # 性能指标
    perf_metrics = stats['performance_metrics']
    logger.info(f"性能: 频率={perf_metrics['callback_frequency_hz']:.1f}Hz, "
               f"响应时间={perf_metrics['avg_response_time_ms']:.1f}ms, "
               f"等级={perf_metrics['overall_performance']}")
    
    # 连接统计
    conn_stats = stats['connection_stats']
    logger.info(f"连接: 成功率={conn_stats.get('success_rate', 0):.1%}, "
               f"可用性={conn_stats.get('availability', 0):.1%}")
    
    logger.info("--- 报告结束 ---")


def main():
    """主函数"""
    logger.info("开始RS485夹爪线程池组件使用演示")
    
    try:
        # 演示各种功能
        demonstrate_basic_usage()
        time.sleep(2)
        
        demonstrate_high_frequency_monitoring()
        time.sleep(2)
        
        # 注意：以下演示需要真实的串口设备
        # demonstrate_gripper_operations()
        # time.sleep(2)
        
        demonstrate_connection_management()
        
        logger.info("所有演示完成 ✅")
        
    except KeyboardInterrupt:
        logger.info("收到停止信号")
    except Exception as e:
        logger.error(f"演示过程中发生异常: {e}")
        raise
    
    logger.info("演示程序结束")


if __name__ == "__main__":
    main()
