# 夹爪控制系统演示脚本

本目录包含各种演示脚本，展示夹爪控制系统的功能和使用方法。

## 📁 脚本列表

### `can_gripper_demo.py` - CAN夹爪专用演示

**功能**: 专门针对CAN夹爪的完整功能演示，包括基本控制、力矩控制和速度控制。

**使用方法**:
```bash
# 需要root权限访问CAN设备
sudo python3 examples/can_gripper_demo.py
```

**演示内容**:
1. **基本控制演示**
   - 完全打开/闭合
   - 中间位置控制
   - 任意位置控制
   - 实时状态监控

2. **力矩控制演示**
   - 不同力矩等级测试 (0x20 到 0xFF)
   - 力矩反馈监控
   - 闭合力度对比

3. **速度控制演示**
   - 不同速度等级测试 (0x20 到 0xFF)
   - 速度参数验证
   - 运动性能对比

**适用场景**:
- CAN夹爪功能验证
- 参数调试和优化
- 性能测试和评估
- 学习CAN夹爪控制方法

## 🚀 快速开始

### 1. 硬件准备

**CAN夹爪要求**:
- 夹爪已连接到CAN总线
- 夹爪已上电并正常工作
- 节点ID已正确配置（默认为1）
- CAN适配器已连接到计算机

### 2. 权限配置

CAN设备需要root权限访问：

```bash
# 方法1: 使用sudo运行（推荐）
sudo python3 examples/can_gripper_demo.py

# 方法2: 配置用户组权限（可选）
sudo usermod -a -G dialout $USER
# 注销重新登录后生效
```

### 3. 运行演示

```bash
# 进入项目根目录
cd /path/to/gripper

# 运行CAN夹爪演示
sudo python3 examples/can_gripper_demo.py
```

### 4. 交互式操作

演示脚本会询问是否继续各个演示环节：

```
==================================================
CAN夹爪基本控制演示
==================================================
创建CAN夹爪 (节点ID=1, 通道=0)...
✓ 夹爪创建成功

等待初始状态...
[状态] 位置:0x80(128) 状态:到位 力矩: 12 故障:0

开始控制演示:
------------------------------

完全打开 (位置: 0xFF)
...

是否继续力矩控制演示? (y/n): y
```

## 🔧 配置说明

### CAN夹爪配置

演示脚本使用以下默认配置：

```python
# 默认配置
node_id = 1      # 节点ID
channel = 0      # CAN通道
device_type = VCI_USBCAN2  # 设备类型

# 波特率配置
# 1Mbps，80%采样点
Timing0 = 0x00
Timing1 = 0x14
```

### 修改配置

如需修改配置，编辑脚本中的相关参数：

```python
# 修改节点ID
gripper = GripperCAN(node_id=2, channel=0)

# 修改通道
gripper = GripperCAN(node_id=1, channel=1)
```

## 📊 状态信息说明

### 状态回调格式

```python
[状态] 位置:0x80(128) 状态:到位 力矩: 12 故障:0
```

**字段说明**:
- **位置**: 当前位置 (十六进制和十进制)
- **状态**: 运动状态描述
- **力矩**: 当前力矩值
- **故障**: 故障代码

### 状态码含义

| 状态码 | 含义 | 故障码 | 含义 |
|--------|------|--------|------|
| 0 | 已达到目标位置 | 0 | 无故障 |
| 1 | 夹爪移动中 | 1 | 过温警报 |
| 2 | 夹爪堵转 | 2 | 超速警报 |
| 3 | 物体掉落 | 3 | 初始化失败 |

## ⚠️ 注意事项

### 安全提醒

1. **确保夹爪周围安全** - 运行演示前确保夹爪周围无障碍物
2. **监控夹爪状态** - 注意观察力矩和故障信息
3. **紧急停止** - 可随时按 `Ctrl+C` 中断演示

### 常见问题

**问题**: `VCI_OpenDevice failed`
**解决**: 
- 使用 `sudo` 运行脚本
- 检查CAN设备连接
- 确认设备驱动正常

**问题**: 夹爪无回复
**解决**:
- 检查夹爪是否上电
- 确认节点ID配置
- 检查CAN总线连接

**问题**: 权限错误
**解决**:
```bash
sudo python3 examples/can_gripper_demo.py
```

## 📝 日志记录

演示脚本会自动记录详细日志：

```bash
# 查看日志
tail -f logs/gripper_*.log

# 日志位置
logs/gripper/gripper_YYYYMMDD_HHMMSS.log
```

## 🔄 扩展开发

### 添加新演示

1. **复制现有脚本**作为模板
2. **修改演示内容**和参数
3. **更新文档**说明新功能
4. **测试验证**确保正常工作

### 自定义配置

```python
# 自定义演示序列
demo_sequence = [
    (0xFF, "完全打开", 3),
    (0x00, "完全闭合", 3),
    (0x7F, "中间位置", 2),
    # 添加更多位置...
]

# 自定义力矩测试
force_levels = [0x20, 0x40, 0x60, 0x80, 0xA0, 0xC0, 0xFF]

# 自定义速度测试
speed_levels = [0x20, 0x40, 0x80, 0xC0, 0xFF]
```

---

## 📞 技术支持

如遇到问题，请：

1. 查看日志文件获取详细错误信息
2. 确认硬件连接和配置
3. 运行基础测试验证环境
4. 提供详细的错误描述和日志

---

**开始体验CAN夹爪的强大功能！** 🚀
