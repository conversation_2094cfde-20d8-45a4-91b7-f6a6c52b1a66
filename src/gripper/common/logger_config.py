#!/usr/bin/env python3
"""
通用日志配置模块
提供类似glog的功能，支持多种日志级别和输出方式
可在多个项目中复用
"""

import logging
import os
import sys
from datetime import datetime
from typing import Optional


class LoggerConfig:
    """
    通用日志配置管理器
    提供类似glog的功能，支持文件和控制台输出
    """
    
    _loggers = {}  # 存储不同名称的logger实例
    
    @classmethod
    def get_logger(cls, name: str = 'default', 
                   log_dir: Optional[str] = None,
                   log_level: int = logging.INFO,
                   console_level: int = logging.INFO,
                   file_level: int = logging.DEBUG,
                   log_format: Optional[str] = None,
                   date_format: Optional[str] = None) -> logging.Logger:
        """
        获取或创建logger实例
        
        :param name: logger名称，不同名称会创建不同的logger
        :param log_dir: 日志文件目录，None表示当前目录
        :param log_level: logger总体级别
        :param console_level: 控制台输出级别
        :param file_level: 文件输出级别
        :param log_format: 日志格式字符串
        :param date_format: 时间格式字符串
        :return: logger实例
        """
        
        # 如果已存在同名logger，直接返回
        if name in cls._loggers:
            return cls._loggers[name]
        
        # 设置默认值
        if log_dir is None:
            log_dir=os.path.join(os.getcwd(), "logs")
            
        
        if log_format is None:
            log_format = '%(asctime)s [%(levelname)s] %(filename)s:%(lineno)d - %(message)s'
        
        if date_format is None:
            date_format = '%Y-%m-%d %H:%M:%S'
        
        # 确保日志目录存在
        if not os.path.exists(log_dir):
            try:
                os.makedirs(log_dir, exist_ok=True)
            except Exception as e:
                print(f"警告: 无法创建日志目录 {log_dir}: {e}")
                log_dir = os.getcwd()  # 回退到当前目录

        # 创建日志文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        log_filename = f"{name}_{timestamp}.log"
        log_path = os.path.join(log_dir, log_filename)

        # 创建logger
        logger = logging.getLogger(name)
        logger.setLevel(log_level)

        # 清除已有的handlers（避免重复添加）
        logger.handlers.clear()

        # 创建格式化器
        formatter = logging.Formatter(log_format, date_format)

        # 文件handler
        try:
            file_handler = logging.FileHandler(log_path, encoding='utf-8')
            file_handler.setLevel(file_level)
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
        except Exception as e:
            print(f"警告: 无法创建日志文件 {log_path}: {e}")
        
        # 控制台handler
        console_handler = logging.StreamHandler(sys.stderr)
        console_handler.setLevel(console_level)
        
        # 控制台使用简化格式
        console_format = '%(asctime)s [%(levelname)s] - %(message)s'
        console_formatter = logging.Formatter(console_format, date_format)
        console_handler.setFormatter(console_formatter)
        logger.addHandler(console_handler)
        
        # 记录logger创建信息
        logger.info(f"日志系统初始化完成，日志文件: {log_path}")
        
        # 缓存logger
        cls._loggers[name] = logger
        
        return logger
    
  
    
    @classmethod
    def set_global_level(cls, level: int):
        """
        设置所有已创建logger的级别
        
        :param level: 日志级别 (logging.DEBUG, INFO, WARNING, ERROR)
        """
        for logger in cls._loggers.values():
            logger.setLevel(level)
    
    @classmethod
    def disable_console_output(cls):
        """禁用所有logger的控制台输出"""
        for logger in cls._loggers.values():
            for handler in logger.handlers:
                if isinstance(handler, logging.StreamHandler) and handler.stream == sys.stderr:
                    handler.setLevel(logging.CRITICAL + 1)  # 设置为不可能的级别
    
    @classmethod
    def enable_console_output(cls, level: int = logging.INFO):
        """启用所有logger的控制台输出"""
        for logger in cls._loggers.values():
            for handler in logger.handlers:
                if isinstance(handler, logging.StreamHandler) and handler.stream == sys.stderr:
                    handler.setLevel(level)
    
    @classmethod
    def get_all_loggers(cls) -> dict:
        """获取所有已创建的logger"""
        return cls._loggers.copy()
    
    @classmethod
    def close_all_loggers(cls):
        """关闭所有logger的文件handler"""
        for logger in cls._loggers.values():
            for handler in logger.handlers[:]:  # 使用切片复制避免修改时迭代
                if isinstance(handler, logging.FileHandler):
                    handler.close()
                    logger.removeHandler(handler)
        cls._loggers.clear()
        
    @classmethod
    def create_gripper_logger(cls) -> logging.Logger:
        """
        创建专用于夹爪系统的logger
        使用预设的配置
        """
        return cls.get_logger(
            name='gripper',
            log_dir=os.path.join(os.getcwd(), "logs/gripper"),
            log_level=logging.DEBUG,
            console_level=logging.INFO,
            file_level=logging.DEBUG
        )
    
    @classmethod
    def create_test_logger(cls) -> logging.Logger:
        """
        创建专用于测试的logger
        使用预设的配置
        """
        return cls.get_logger(
            name='test',
            log_dir=os.path.join(os.getcwd(), "logs/test"),
            log_level=logging.DEBUG,
            console_level=logging.WARNING,  # 测试时控制台输出较少
            file_level=logging.DEBUG
        )


# 便捷函数
def get_logger(name: str = 'default', **kwargs) -> logging.Logger:
    """便捷函数：获取logger实例"""
    return LoggerConfig.get_logger(name, **kwargs)


def get_gripper_logger() -> logging.Logger:
    """便捷函数：获取夹爪专用logger"""
    return LoggerConfig.create_gripper_logger()


def get_test_logger() -> logging.Logger:
    """便捷函数：获取测试专用logger"""
    return LoggerConfig.create_test_logger()


# 示例用法
# if __name__ == '__main__':
#     # 创建不同类型的logger
#     main_logger = get_logger('main')
#     gripper_logger = get_gripper_logger()
#     test_logger = get_test_logger()
    
#     # 测试日志输出
#     main_logger.info("这是主程序日志")
#     main_logger.warning("这是警告信息")
#     main_logger.error("这是错误信息")
    
#     gripper_logger.info("夹爪系统启动")
#     gripper_logger.debug("夹爪调试信息")
    
#     test_logger.info("测试开始")
#     test_logger.debug("测试调试信息")
    
#     # 显示所有logger
#     print("已创建的logger:")
#     for name, logger in LoggerConfig.get_all_loggers().items():
#         print(f"  {name}: {logger}")
    
#     # 清理
#     LoggerConfig.close_all_loggers()
#     print("所有logger已关闭")
