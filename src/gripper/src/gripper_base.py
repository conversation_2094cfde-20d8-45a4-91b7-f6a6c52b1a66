#!/usr/bin/env python3
"""
夹爪抽象基类
定义所有夹爪的通用接口和抽象方法
"""

from abc import ABC, abstractmethod
from typing import Optional, Dict, Any, Callable
from enum import Enum
import threading
import time


class GripperType(Enum):
    """夹爪类型枚举"""
    CAN = "can"
    RS485 = "rs485"


class GripperState(Enum):
    """夹爪状态枚举"""
    TARGET_REACHED = 0x00  # 到达目标位置
    MOVING = 0x01          # 正在移动
    STALLED = 0x02         # 堵转
    OBJECT_DROPPED = 0x03  # 物体掉落


class GripperFault(Enum):
    """夹爪故障代码枚举"""
    NO_FAULT = 0x00        # 无故障
    OVERTEMP = 0x01        # 过温
    OVERSPEED = 0x02       # 超速
    INIT_FAULT = 0x03      # 初始化故障
    LIMIT_DETECTION = 0x04 # 限位检测


class GripperBase(ABC):
    """
    夹爪抽象基类
    定义所有夹爪的通用接口和抽象方法
    """
    
    def __init__(self, node_id: int = 1):
        """
        初始化夹爪基类
        :param node_id: 夹爪节点ID
        """
        self.node_id = node_id
        self._status_callback = None
        self._recv_thread = None
        self._recv_thread_running = False
        self._last_status = None
        self._target_position = 0x80
        self._target_force = 0xFF
        self._target_velocity = 0xFF
        self._target_acceleration = 0xFF
        self._target_deceleration = 0xFF
        self._query_interval = 0.1
        self.logger = None  # 子类需要初始化logger
    
    @abstractmethod
    def connect(self) -> bool:
        """
        连接到夹爪设备
        :return: 连接成功返回True，失败返回False
        """
        pass
    
    @abstractmethod
    def disconnect(self) -> bool:
        """
        断开夹爪连接
        :return: 断开成功返回True，失败返回False
        """
        pass
    
    @abstractmethod
    def send_command(self, pos_cmd: int, force_cmd: int = 0xFF, 
                    vel_cmd: int = 0xFF, acc_cmd: int = 0xFF, 
                    dec_cmd: int = 0xFF, timeout: float = 5.0,
                    wait_for_completion: bool = True, 
                    position_tolerance: int = 10) -> Optional[Dict[str, Any]]:
        """
        发送控制指令到夹爪
        :param pos_cmd: 目标位置（0=闭合，0xFF=张开）
        :param force_cmd: 力矩
        :param vel_cmd: 速度
        :param acc_cmd: 加速度
        :param dec_cmd: 减速度
        :param timeout: 超时时间（秒）
        :param wait_for_completion: 是否等待夹爪到达目标位置
        :param position_tolerance: 位置容差（允许的位置误差）
        :return: 接收到的状态字典（成功）或 None（超时/失败）
        """
        pass
    
    @abstractmethod
    def get_status(self, timeout: float = 1.0) -> Optional[Dict[str, Any]]:
        """
        获取夹爪当前状态
        :param timeout: 超时时间（秒）
        :return: 状态字典或None
        """
        pass
    
    @abstractmethod
    def parse_status(self, data: bytes) -> Optional[Dict[str, Any]]:
        """
        解析状态数据
        :param data: 原始状态数据
        :return: 解析后的状态字典
        """
        pass
    
    @abstractmethod
    def close(self):
        """
        关闭夹爪连接和资源
        """
        pass
    
    # 通用便捷方法（子类可以重写以优化实现）
    def open_gripper(self, force: int = 0xFF, timeout: float = 5.0) -> Optional[Dict[str, Any]]:
        """
        打开夹爪
        :param force: 力矩 (0-0xFF)
        :param timeout: 超时时间
        :return: 状态字典或 None
        """
        if self.logger:
            self.logger.info("打开夹爪")
        return self.send_command(0xFF, force_cmd=force, timeout=timeout)
    
    def close_gripper(self, force: int = 0xFF, timeout: float = 5.0) -> Optional[Dict[str, Any]]:
        """
        关闭夹爪
        :param force: 力矩 (0-0xFF)
        :param timeout: 超时时间
        :return: 状态字典或 None
        """
        if self.logger:
            self.logger.info("关闭夹爪")
        return self.send_command(0x00, force_cmd=force, timeout=timeout)
    
    def move_to_position(self, position: int, force: int = 0xFF, 
                        timeout: float = 5.0) -> Optional[Dict[str, Any]]:
        """
        移动到指定位置
        :param position: 目标位置 (0-0xFF, 0=关闭, 0xFF=打开)
        :param force: 力矩 (0-0xFF)
        :param timeout: 超时时间
        :return: 状态字典或 None
        """
        if self.logger:
            self.logger.info(f"移动到位置 0x{position:02X}")
        return self.send_command(position, force_cmd=force, timeout=timeout)
    
    def is_moving(self) -> bool:
        """
        检查夹爪是否正在移动
        :return: True if moving, False otherwise
        """
        if self._last_status:
            return self._last_status.get('state') == GripperState.MOVING.value
        return False
    
    def has_fault(self) -> bool:
        """
        检查夹爪是否有故障
        :return: True if fault exists, False otherwise
        """
        if self._last_status:
            return self._last_status.get('fault_code', 0) != GripperFault.NO_FAULT.value
        return False
    
    def get_position(self) -> Optional[int]:
        """
        获取当前位置
        :return: 当前位置值或None
        """
        if self._last_status:
            return self._last_status.get('position')
        return None
    
    def get_fault_code(self) -> Optional[int]:
        """
        获取故障代码
        :return: 故障代码或None
        """
        if self._last_status:
            return self._last_status.get('fault_code')
        return None
    
    def set_status_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """
        设置状态回调函数
        :param callback: 回调函数，接收状态字典作为参数
        """
        self._status_callback = callback
        if self.logger:
            self.logger.info("设置状态回调函数")
    
    def set_query_interval(self, interval: float):
        """
        设置状态查询间隔
        :param interval: 查询间隔（秒）
        """
        self._query_interval = max(0.01, interval)  # 最小10ms
        if self.logger:
            self.logger.info(f"设置查询间隔为 {self._query_interval:.3f}s")
    
    def wait_for_target(self, timeout: float = 10.0, position_tolerance: int = 10) -> bool:
        """
        等待夹爪到达目标位置
        :param timeout: 超时时间（秒）
        :param position_tolerance: 位置容差
        :return: 是否成功到达目标位置
        """
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            status = self.get_status(timeout=1.0)
            if status:
                current_pos = status.get('position', 0)
                state = status.get('state', 0)
                
                # 检查是否到达目标位置
                if abs(current_pos - self._target_position) <= position_tolerance:
                    if state == GripperState.TARGET_REACHED.value:
                        return True
                
                # 检查是否有故障
                if status.get('fault_code', 0) != GripperFault.NO_FAULT.value:
                    if self.logger:
                        self.logger.error(f"夹爪故障: {status.get('fault_code')}")
                    return False
            
            time.sleep(0.1)
        
        if self.logger:
            self.logger.warning(f"等待目标位置超时 ({timeout}s)")
        return False
    
    def emergency_stop(self) -> bool:
        """
        紧急停止
        :return: 停止成功返回True
        """
        if self.logger:
            self.logger.warning("执行紧急停止")
        # 发送当前位置作为目标位置来停止运动
        current_pos = self.get_position()
        if current_pos is not None:
            result = self.send_command(current_pos, timeout=1.0)
            return result is not None
        return False
    
    def get_gripper_type(self) -> GripperType:
        """
        获取夹爪类型
        :return: 夹爪类型枚举
        """
        # 子类应该重写此方法返回正确的类型
        return GripperType.CAN  # 默认返回CAN类型
    
    def __enter__(self):
        """上下文管理器入口"""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close()
