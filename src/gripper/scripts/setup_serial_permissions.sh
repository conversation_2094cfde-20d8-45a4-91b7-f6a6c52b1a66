#!/bin/bash
# 串口权限设置脚本
# 用于永久解决串口设备权限问题，避免每次都需要sudo运行程序

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否以root权限运行
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        log_info "请使用: sudo $0"
        exit 1
    fi
}

# 备份现有规则（如果存在）
backup_existing_rules() {
    local rule_file="/etc/udev/rules.d/50-gripper-serial.rules"
    if [[ -f "$rule_file" ]]; then
        local backup_file="${rule_file}.backup.$(date +%Y%m%d_%H%M%S)"
        log_info "备份现有规则文件到: $backup_file"
        cp "$rule_file" "$backup_file"
    fi
}

# 创建udev规则
create_udev_rules() {
    local rule_file="/etc/udev/rules.d/50-gripper-serial.rules"
    
    log_info "创建udev规则文件: $rule_file"
    
    cat > "$rule_file" << 'EOF'
# 夹爪串口设备权限规则
# 自动为串口设备设置适当的权限，允许dialout组用户访问

# USB转串口设备 (ttyUSB*)
KERNEL=="ttyUSB[0-9]*", GROUP="dialout", MODE="0666", TAG+="uaccess"

# USB ACM设备 (ttyACM*)
KERNEL=="ttyACM[0-9]*", GROUP="dialout", MODE="0666", TAG+="uaccess"

# 其他常见串口设备
KERNEL=="ttyS[0-9]*", GROUP="dialout", MODE="0666", TAG+="uaccess"

# 特定厂商的USB设备（可根据需要添加）
# 例如：FTDI芯片
SUBSYSTEM=="usb", ATTRS{idVendor}=="0403", ATTRS{idProduct}=="6001", GROUP="dialout", MODE="0666"

# CH340/CH341芯片
SUBSYSTEM=="usb", ATTRS{idVendor}=="1a86", ATTRS{idProduct}=="7523", GROUP="dialout", MODE="0666"

# CP210x芯片
SUBSYSTEM=="usb", ATTRS{idVendor}=="10c4", ATTRS{idProduct}=="ea60", GROUP="dialout", MODE="0666"
EOF

    if [[ $? -eq 0 ]]; then
        log_success "udev规则文件创建成功"
    else
        log_error "udev规则文件创建失败"
        exit 1
    fi
}

# 将用户添加到dialout组
add_user_to_dialout() {
    local username="$1"
    
    if [[ -z "$username" ]]; then
        log_error "用户名不能为空"
        return 1
    fi
    
    log_info "将用户 $username 添加到 dialout 组"
    
    if usermod -a -G dialout "$username"; then
        log_success "用户 $username 已添加到 dialout 组"
    else
        log_error "添加用户到 dialout 组失败"
        return 1
    fi
}

# 重新加载udev规则
reload_udev_rules() {
    log_info "重新加载udev规则..."
    
    if udevadm control --reload-rules; then
        log_success "udev规则重新加载成功"
    else
        log_error "udev规则重新加载失败"
        return 1
    fi
    
    log_info "触发udev事件..."
    if udevadm trigger; then
        log_success "udev事件触发成功"
    else
        log_warning "udev事件触发可能失败，但不影响规则生效"
    fi
}

# 检查当前串口设备
check_serial_devices() {
    log_info "检查当前串口设备:"
    
    local devices_found=false
    
    for device in /dev/ttyUSB* /dev/ttyACM* /dev/ttyS*; do
        if [[ -e "$device" ]]; then
            devices_found=true
            local perms=$(ls -l "$device")
            echo "  $device: $perms"
        fi
    done
    
    if [[ "$devices_found" == false ]]; then
        log_warning "未找到串口设备，请连接设备后重新检查"
    fi
}

# 验证设置
verify_setup() {
    local username="$1"
    
    log_info "验证设置..."
    
    # 检查用户是否在dialout组中
    if groups "$username" | grep -q dialout; then
        log_success "用户 $username 已在 dialout 组中"
    else
        log_error "用户 $username 不在 dialout 组中"
        return 1
    fi
    
    # 检查udev规则文件
    if [[ -f "/etc/udev/rules.d/50-gripper-serial.rules" ]]; then
        log_success "udev规则文件存在"
    else
        log_error "udev规则文件不存在"
        return 1
    fi
    
    log_success "设置验证完成"
}

# 显示使用说明
show_usage() {
    echo "用法: $0 [选项] [用户名]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示此帮助信息"
    echo "  -c, --check    仅检查当前串口设备状态"
    echo ""
    echo "参数:"
    echo "  用户名         要添加到dialout组的用户名（默认为当前sudo用户）"
    echo ""
    echo "示例:"
    echo "  sudo $0                    # 为当前用户设置权限"
    echo "  sudo $0 username           # 为指定用户设置权限"
    echo "  sudo $0 --check            # 仅检查设备状态"
}

# 主函数
main() {
    echo "========================================"
    echo "    夹爪串口权限设置脚本"
    echo "========================================"
    echo ""
    
    # 解析命令行参数
    case "${1:-}" in
        -h|--help)
            show_usage
            exit 0
            ;;
        -c|--check)
            check_serial_devices
            exit 0
            ;;
    esac
    
    # 检查root权限
    check_root
    
    # 确定目标用户
    local target_user="${1:-$SUDO_USER}"
    if [[ -z "$target_user" ]]; then
        log_error "无法确定目标用户，请指定用户名"
        show_usage
        exit 1
    fi
    
    log_info "开始为用户 $target_user 设置串口权限..."
    
    # 执行设置步骤
    backup_existing_rules
    create_udev_rules
    add_user_to_dialout "$target_user"
    reload_udev_rules
    
    echo ""
    log_info "检查当前设备状态:"
    check_serial_devices
    
    echo ""
    verify_setup "$target_user"
    
    echo ""
    echo "========================================"
    log_success "串口权限设置完成！"
    echo "========================================"
    echo ""
    log_warning "重要提醒:"
    echo "1. 用户 $target_user 需要重新登录才能使组权限生效"
    echo "2. 如果设备已连接，请重新插拔一次以应用新规则"
    echo "3. 现在可以不使用sudo运行夹爪程序了"
    echo ""
    log_info "测试命令: python3 main.py"
}

# 运行主函数
main "$@"
