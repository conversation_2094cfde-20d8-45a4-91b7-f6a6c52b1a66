#!/usr/bin/env python3
import rclpy
from rclpy.node import Node
from geometry_msgs.msg import Po<PERSON>, Point, Quaternion
from ymrobot import <PERSON><PERSON><PERSON>bot
from sensor_msgs.msg import JointState
import math
import time

class YMrobotNode(Node):
    def __init__(self):
        super().__init__('ymrobot_node')
        self.robot = YMrobot()

        # Example: Control right arm joints
        # right_arm_joints = [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]
        # self.robot.control_right_arm_joint_position(right_arm_joints)

        # # Example: Control both arms' end-effectors
        # left_pose = Pose(position=Point(x=0.40, y=0.28, z=0.85), orientation=Quaternion(x=-0.0343, y=0.1710, z=-0.0670, w=0.9819)) # 末端斜向下 20°，偏右 15°
        # right_pose = Pose(position=Point(x=0.32, y=-0.28, z=0.83), orientation=Quaternion(x=0.0, y=0.2588, z=0.0, w=0.9659))
        # self.robot.control_both_arms_end_effector(left_pose, right_pose)
        time.sleep(2.0)  # Wait for action to complete

        # # Example: Get joint states
        joint_states = self.robot.get_full_joint_state()
        if joint_states:
            self.get_logger().info(f"Joint states: {joint_states}")
        print("joint_states",joint_states)

        # # Example: Get head RGB
        # head_rgb, head_rgb_stamp = self.robot.get_head_rgb()
        # if head_rgb is not None:
        #     self.get_logger().info(f"Head RGB shape: {head_rgb.shape}")
        # print(f"Head RGB timestamp: {head_rgb_stamp}")

        # # Example: Reset
        # self.robot.reset()

    def destroy_node(self):
        self.robot.shutdown()
        super().destroy_node()

def main(args=None):
    rclpy.init(args=args)
    node = YMrobotNode()
    rclpy.spin(node)
    node.destroy_node()
    rclpy.shutdown()

if __name__ == '__main__':
    main()