from launch import LaunchDescription
from launch_ros.actions import Node
from launch.actions import ExecuteProcess
from launch.actions import IncludeLaunchDescription
from launch.substitutions import Command, FindExecutable, PathJoinSubstitution
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch_ros.substitutions import FindPackageShare
from ament_index_python.packages import get_package_share_directory
import os

def generate_launch_description():

    
    pkg_ymbot_d_moveit = get_package_share_directory("ymbot_d_moveit_config")

    rsp_path = os.path.join(
        get_package_share_directory('ymbot_d_moveit_config'),  # 替换为你的包名
        'launch',
        'rsp.launch.py'  # 子 launch 文件名
    )
    rsp = IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
                rsp_path
            ),
        )

    group_path = os.path.join(
        get_package_share_directory('ymbot_d_moveit_config'),  # 替换为你的包名
        'launch',
        'move_group.launch.py'  # 子 launch 文件名
    )
    group = IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
                group_path
            ),
        )


    robot_controlers = os.path.join(
        get_package_share_directory('udp2joint'),
        'config',
        'controllers.yaml'
    )
    controller_manager = Node(
            package="controller_manager",
            executable="ros2_control_node",
            parameters=[robot_controlers],
            output="screen",
        )
    
    
    robot_description_semantic_path = os.path.join(pkg_ymbot_d_moveit, 'config', 'ymbot_d.srdf')
    with open(robot_description_semantic_path, 'r') as f:
        robot_description_semantic = f.read()
    
    robot_description = Command(
        [
            PathJoinSubstitution([FindExecutable(name="xacro")]),
            " ",
            PathJoinSubstitution(
                [
                    FindPackageShare("ymbot_d_moveit_config"),
                    "config",
                    "ymbot_d.urdf.xacro",
                ]
            )
            
        ]
    )
    robot_state_publisher = Node(
        package='robot_state_publisher',
        executable='robot_state_publisher',
        name='robot_state_publisher',
        parameters=[{
            'robot_description': robot_description,
            'robot_description_semantic': robot_description_semantic
        }]
    )

    rviz = Node(
            package='rviz2',
            executable='rviz2',
            name='rviz2',
            arguments=['-d', os.path.join(get_package_share_directory('udp2joint'), 'rviz', 'rviz2.rviz')],
            parameters=[{'required': True}]
    )

    spawner = Node(
        package="controller_manager",
        executable="spawner",
        arguments=[
            "joint_state_broadcaster"
        ],
        output='screen'
    )
    left_arm_spawner = Node(
        package="controller_manager",
        executable="spawner",
        arguments=[
            "left_arm_position_controller",
            "--param-file",
            robot_controlers
        ],
        output='screen'
    )
    right_arm_spawner = Node(
        package="controller_manager",
        executable="spawner",
        arguments=[
            "right_arm_position_controller",
            "--param-file",
            robot_controlers
        ],
        output='screen'
    )

    hands = ExecuteProcess(
            cmd=['python3', '/home/<USER>/ws_ros2/src/inspire_hand/inspire_hand/single_hand_command.py'],  # 指定 Python 解释器和脚本路径
            output='screen'  # 将输出打印到屏幕
        )

    
    
     # 返回启动描述
    return LaunchDescription([
        rsp,
        group,
        controller_manager,
        # robot_state_publisher,
        rviz,
        spawner,
        left_arm_spawner,
        right_arm_spawner,
        hands
    ]) 
